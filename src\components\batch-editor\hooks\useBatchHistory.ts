'use client';

import { useCallback } from 'react';
import { useImageStore } from '@/lib/store/imageStore';
import {
  useBatchHistoryStore,
  batchHistoryActions,
  type ImageStateSnapshot,
} from '@/lib/store/batchHistoryStore';

/**
 * 批量历史记录管理Hook
 * 提供批量编辑器的历史记录功能，包括前进、后退、记录操作等
 */
export const useBatchHistory = () => {
  // 获取批量历史记录状态
  const { canUndo, canRedo, history, currentIndex } = useBatchHistoryStore();

  // 获取图片store的方法
  const imageStore = useImageStore.getState();

  /**
   * 创建图片状态快照
   */
  const createImageSnapshot = useCallback(
    (imageIds: string[]) => {
      const snapshot: Record<string, ImageStateSnapshot> = {};

      imageIds.forEach(imageId => {
        const image = imageStore.images.get(imageId);
        if (image) {
          // 创建图片状态的深拷贝
          snapshot[imageId] = {
            id: image.id,
            status: image.status,
            backgroundColor: image.backgroundColor,
            backgroundImageUrl: image.backgroundImageUrl,
            backgroundImageId: image.backgroundImageId,
            targetWidth: image.targetWidth,
            targetHeight: image.targetHeight,
            processedUrl: image.processedUrl,
            compressedUrl: image.compressedUrl,
            compressedSize: image.compressedSize,
            convertedFormat: image.convertedFormat,
            convertedUrl: image.convertedUrl,
            // 添加其他需要记录的状态
          };
        }
      });

      return snapshot;
    },
    [imageStore]
  );

  /**
   * 恢复图片状态
   */
  const restoreImageStates = useCallback(
    (imageStates: Record<string, ImageStateSnapshot>) => {
      console.log(
        '开始恢复图片状态:',
        Object.keys(imageStates).length,
        '张图片'
      );

      Object.entries(imageStates).forEach(([imageId, imageState]) => {
        const currentImage = imageStore.images.get(imageId);
        if (currentImage) {
          // 恢复图片状态
          imageStore.updateImage(imageId, {
            status: imageState.status,
            backgroundColor: imageState.backgroundColor,
            backgroundImageUrl: imageState.backgroundImageUrl,
            backgroundImageId: imageState.backgroundImageId,
            targetWidth: imageState.targetWidth,
            targetHeight: imageState.targetHeight,
            processedUrl: imageState.processedUrl,
            compressedUrl: imageState.compressedUrl,
            compressedSize: imageState.compressedSize,
            convertedFormat: imageState.convertedFormat,
            convertedUrl: imageState.convertedUrl,
          });
        }
      });

      console.log('图片状态恢复完成');
    },
    [imageStore]
  );

  /**
   * 记录批量操作到历史记录（在操作完成后调用）
   */
  const recordBatchOperation = useCallback(
    (
      operation: 'background' | 'resize' | 'compress' | 'convert' | 'rename',
      description: string,
      imageIds: string[]
    ) => {
      // 创建操作后的状态快照
      const imageStates = createImageSnapshot(imageIds);

      // 添加到历史记录
      batchHistoryActions.addHistory({
        operation,
        description,
        imageStates,
      });

      console.log(`批量操作已记录: ${operation} - ${description}`, {
        imageCount: imageIds.length,
        historyLength: useBatchHistoryStore.getState().getHistoryLength(),
      });
    },
    [createImageSnapshot]
  );

  /**
   * 记录初始状态（在第一次操作前调用）
   */
  const recordInitialState = useCallback(
    (imageIds: string[]) => {
      const historyLength = useBatchHistoryStore.getState().getHistoryLength();

      // 只在没有历史记录时记录初始状态
      if (historyLength === 0) {
        const imageStates = createImageSnapshot(imageIds);

        batchHistoryActions.addHistory({
          operation: 'background',
          description: '初始状态',
          imageStates,
        });

        console.log('已记录初始状态', {
          imageCount: imageIds.length,
        });
      }
    },
    [createImageSnapshot]
  );

  /**
   * 撤销操作
   */
  const handleUndo = useCallback(() => {
    if (!canUndo) {
      console.warn('无法撤销：没有可撤销的操作');
      return false;
    }

    // 执行撤销操作，这会改变currentIndex
    const undoResult = batchHistoryActions.undo();
    if (undoResult) {
      // 获取撤销后应该恢复到的状态（当前索引指向的状态）
      const currentHistoryItem = useBatchHistoryStore
        .getState()
        .getCurrentHistoryItem();

      if (currentHistoryItem) {
        // 恢复到撤销后的状态
        restoreImageStates(currentHistoryItem.imageStates);

        // 异步更新预览URL
        const imageIds = Object.keys(currentHistoryItem.imageStates);
        imageStore.batchUpdatePreviewUrls(imageIds).catch(error => {
          console.error('批量更新预览URL失败:', error);
        });

        console.log(
          `已撤销到: ${currentHistoryItem.operation} - ${currentHistoryItem.description}`
        );
        return true;
      }
    }

    return false;
  }, [canUndo, restoreImageStates, imageStore]);

  /**
   * 重做操作
   */
  const handleRedo = useCallback(() => {
    if (!canRedo) {
      console.warn('无法重做：没有可重做的操作');
      return false;
    }

    // 执行重做操作，这会改变currentIndex
    const redoResult = batchHistoryActions.redo();
    if (redoResult) {
      // 获取重做后应该恢复到的状态（当前索引指向的状态）
      const currentHistoryItem = useBatchHistoryStore
        .getState()
        .getCurrentHistoryItem();

      if (currentHistoryItem) {
        // 恢复到重做后的状态
        restoreImageStates(currentHistoryItem.imageStates);

        // 异步更新预览URL
        const imageIds = Object.keys(currentHistoryItem.imageStates);
        imageStore.batchUpdatePreviewUrls(imageIds).catch(error => {
          console.error('批量更新预览URL失败:', error);
        });

        console.log(
          `已重做到: ${currentHistoryItem.operation} - ${currentHistoryItem.description}`
        );
        return true;
      }
    }

    return false;
  }, [canRedo, restoreImageStates, imageStore]);

  /**
   * 清空历史记录
   */
  const clearHistory = useCallback(() => {
    batchHistoryActions.clear();
    console.log('批量历史记录已清空');
  }, []);

  /**
   * 获取当前历史记录信息
   */
  const getHistoryInfo = useCallback(() => {
    return {
      canUndo,
      canRedo,
      currentIndex,
      historyLength: history.length,
      currentOperation:
        currentIndex >= 0 ? history[currentIndex]?.operation : null,
    };
  }, [canUndo, canRedo, currentIndex, history]);

  return {
    // 状态
    canUndo,
    canRedo,
    history,
    currentIndex,

    // 操作方法
    recordBatchOperation,
    recordInitialState,
    handleUndo,
    handleRedo,
    clearHistory,
    getHistoryInfo,

    // 工具方法
    createImageSnapshot,
    restoreImageStates,
  };
};
