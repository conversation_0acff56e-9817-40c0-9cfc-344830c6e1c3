import { useEffect, useState, useCallback, useRef } from 'react';
import {
  useImageStore,
  initializeImageStorage,
  loadPersistedImages,
} from '@/lib/store/imageStore';
import { imageStorage } from '@/lib/storage/indexeddb-storage';
import type { UploadedImage } from '../components/BackgroundImagePicker';

/**
 * 图片存储管理Hook
 * 处理IndexedDB存储的初始化、加载和状态管理
 */
export const useImageStorage = () => {
  const [initializationStatus, setInitializationStatus] = useState<
    'idle' | 'initializing' | 'success' | 'error'
  >('idle');
  const [initializationError, setInitializationError] = useState<string | null>(
    null
  );

  // 从store获取存储相关状态
  const isStorageInitialized = useImageStore(s => s.isStorageInitialized);
  const isStorageLoading = useImageStore(s => s.isStorageLoading);
  const isSaving = useImageStore(s => s.isSaving);
  const autoSaveEnabled = useImageStore(s => s.autoSaveEnabled);
  const images = useImageStore(s => s.images);

  // 获取存储相关方法
  const { cleanupOldImages, setAutoSave, saveAllImagesToPersistentStorage } =
    useImageStore.getState();

  // 自定义背景图片状态
  const [uploadedBackgroundImages, setUploadedBackgroundImages] = useState<
    UploadedImage[]
  >([]);
  const [isLoadingBackgroundImages, setIsLoadingBackgroundImages] =
    useState(true);

  // 使用ref来跟踪加载状态，避免依赖循环
  const isLoadingRef = useRef(false);

  // 调试：监听uploadedBackgroundImages变化
  useEffect(() => {
    console.log(
      `🔄 uploadedBackgroundImages 状态更新: ${uploadedBackgroundImages.length} 张图片`
    );
    uploadedBackgroundImages.forEach((img, index) => {
      console.log(`  ${index + 1}. ${img.name} (${img.id}) - ${img.url}`);
    });
  }, [uploadedBackgroundImages]);

  /**
   * 初始化存储并加载持久化数据
   */
  const initializeAndLoadStorage = useCallback(async () => {
    if (
      initializationStatus === 'initializing' ||
      initializationStatus === 'success'
    ) {
      return;
    }

    try {
      setInitializationStatus('initializing');
      setInitializationError(null);

      // 步骤1: 初始化IndexedDB
      const storageInitialized = await initializeImageStorage();
      if (!storageInitialized) {
        throw new Error('存储初始化失败');
      }

      // 步骤2: 加载持久化的图片
      const imagesLoaded = await loadPersistedImages();
      if (!imagesLoaded) {
        console.warn('加载持久化图片失败，但存储初始化成功');
      }

      setInitializationStatus('success');
    } catch (error) {
      console.error('存储初始化失败:', error);
      setInitializationError(
        error instanceof Error ? error.message : '未知错误'
      );
      setInitializationStatus('error');
    }
  }, [initializationStatus]);

  /**
   * 手动保存所有图片
   */
  const saveAllImages = useCallback(async () => {
    try {
      await saveAllImagesToPersistentStorage();
      return true;
    } catch (error) {
      console.error('保存所有图片失败:', error);
      return false;
    }
  }, [saveAllImagesToPersistentStorage]);

  /**
   * 清理旧图片
   */
  const cleanupStorage = useCallback(
    async (keepCount: number = 10) => {
      try {
        const deletedCount = await cleanupOldImages(keepCount);
        return deletedCount;
      } catch (error) {
        console.error('存储清理失败:', error);
        return 0;
      }
    },
    [cleanupOldImages]
  );

  /**
   * 切换自动保存
   */
  const toggleAutoSave = useCallback(
    (enabled?: boolean) => {
      const newState = enabled !== undefined ? enabled : !autoSaveEnabled;
      setAutoSave(newState);
      return newState;
    },
    [autoSaveEnabled, setAutoSave]
  );

  /**
   * 自动初始化
   * 当hook首次使用时自动初始化存储
   */
  useEffect(() => {
    let isCancelled = false;

    if (initializationStatus === 'idle') {
      // 延迟初始化，避免阻塞首屏渲染
      const timer = setTimeout(() => {
        if (!isCancelled) {
          initializeAndLoadStorage();
        }
      }, 100);

      return () => {
        isCancelled = true;
        clearTimeout(timer);
      };
    }

    return undefined;
  }, [initializeAndLoadStorage, initializationStatus]);

  /**
   * 监听页面卸载，保存当前状态
   */
  useEffect(() => {
    if (!isStorageInitialized || !autoSaveEnabled) {
      return;
    }

    const handleBeforeUnload = () => {
      // 在页面卸载前尝试快速保存
      // 注意：这里不能使用async/await，因为可能来不及执行
      try {
        saveAllImagesToPersistentStorage();
      } catch (error) {
        console.warn('页面卸载时保存失败:', error);
      }
    };

    const handleVisibilityChange = () => {
      // 当页面变为隐藏时（用户切换标签页等），保存当前状态
      if (document.visibilityState === 'hidden') {
        saveAllImages();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [
    isStorageInitialized,
    autoSaveEnabled,
    saveAllImages,
    saveAllImagesToPersistentStorage,
  ]);

  // 加载自定义背景图片
  const loadBackgroundImages = useCallback(async () => {
    if (!isStorageInitialized) {
      console.log('跳过背景图片加载: 存储未初始化');
      return;
    }

    // 防止重复加载
    if (isLoadingRef.current) {
      console.log('跳过背景图片加载: 正在加载中');
      return;
    }

    try {
      console.log('开始加载背景图片...');
      isLoadingRef.current = true;
      setIsLoadingBackgroundImages(true);

      const allBackgroundImages = await imageStorage.getAllBackgroundImages();
      console.log(
        `从IndexedDB获取到 ${allBackgroundImages.length} 个背景图片元数据`
      );

      const imageList: UploadedImage[] = [];
      for (const metadata of allBackgroundImages) {
        try {
          console.log(`正在加载背景图片: ${metadata.id}`);
          const result = await imageStorage.loadBackgroundImage(metadata.id);
          if (result) {
            imageList.push({
              id: metadata.id,
              url: result.url,
              name: metadata.name,
              timestamp: metadata.timestamp,
              file: result.file,
            });
            console.log(`背景图片加载成功: ${metadata.id}, URL: ${result.url}`);
          } else {
            console.warn(`背景图片加载结果为空: ${metadata.id}`);
          }
        } catch (error) {
          console.error(`加载背景图片失败: ${metadata.id}`, error);
          // 加载失败时删除损坏的数据
          try {
            await imageStorage.deleteBackgroundImage(metadata.id);
          } catch (deleteError) {
            console.error(
              `删除损坏的背景图片失败: ${metadata.id}`,
              deleteError
            );
          }
        }
      }

      console.log(`背景图片加载完成，共 ${imageList.length} 张图片`);
      setUploadedBackgroundImages(imageList);
    } catch (error) {
      console.error('加载背景图片失败:', error);
    } finally {
      isLoadingRef.current = false;
      setIsLoadingBackgroundImages(false);
    }
  }, [isStorageInitialized]);

  /**
   * 检查图片URL是否有效
   */
  const checkImageUrlValid = useCallback(
    async (url: string): Promise<boolean> => {
      if (!url.startsWith('blob:')) {
        return true; // 非blob URL默认有效
      }

      return new Promise(resolve => {
        const img = new Image();
        const timeout = setTimeout(() => resolve(false), 500);

        img.onload = () => {
          clearTimeout(timeout);
          resolve(true);
        };

        img.onerror = () => {
          clearTimeout(timeout);
          resolve(false);
        };

        img.src = url;
      });
    },
    []
  );

  /**
   * 恢复图片的背景图片选中状态
   * 确保图片的backgroundImageUrl与当前加载的背景图片URL同步
   */
  const restoreImageBackgroundStates = useCallback(async () => {
    if (!isStorageInitialized) return;

    try {
      const allImages = Array.from(useImageStore.getState().images.values());
      if (allImages.length === 0) return;

      console.log(`开始恢复 ${allImages.length} 张图片的背景状态...`);

      const updatePromises: Promise<void>[] = [];

      for (const image of allImages) {
        if (image.backgroundImageId) {
          updatePromises.push(
            (async () => {
              console.log(
                `检查图片 ${image.id} 的背景图片: ${image.backgroundImageId}`
              );

              // 查找当前加载的背景图片列表中对应的图片
              const matchedBackground = uploadedBackgroundImages.find(
                bg => bg.id === image.backgroundImageId
              );

              if (matchedBackground) {
                // 检查当前的URL是否与加载的背景图片URL匹配
                if (image.backgroundImageUrl !== matchedBackground.url) {
                  console.log(
                    `更新图片 ${image.id} 的背景URL: ${image.backgroundImageUrl} -> ${matchedBackground.url}`
                  );
                  useImageStore.getState().updateImage(image.id, {
                    backgroundImageUrl: matchedBackground.url,
                  });
                } else {
                  console.log(
                    `图片 ${image.id} 的背景URL已同步: ${matchedBackground.url}`
                  );
                }
              } else {
                console.warn(
                  `未找到图片 ${image.id} 对应的背景图片: ${image.backgroundImageId}`
                );

                // 如果在当前列表中找不到，尝试从IndexedDB重新加载
                if (image.backgroundImageUrl?.startsWith('blob:')) {
                  const isValid = await checkImageUrlValid(
                    image.backgroundImageUrl
                  );

                  if (!isValid) {
                    console.log(
                      `图片 ${image.id} 的背景URL已失效，尝试从IndexedDB重新加载...`
                    );
                    try {
                      const loadResult = await imageStorage.loadBackgroundImage(
                        image.backgroundImageId!
                      );
                      if (loadResult) {
                        console.log(
                          `从IndexedDB重新加载背景图片成功: ${image.backgroundImageId}`
                        );
                        useImageStore.getState().updateImage(image.id, {
                          backgroundImageUrl: loadResult.url,
                        });
                      }
                    } catch (error) {
                      console.error(
                        `从IndexedDB重新加载背景图片失败: ${image.backgroundImageId}`,
                        error
                      );
                    }
                  }
                }
              }
            })()
          );
        }
      }

      if (updatePromises.length > 0) {
        await Promise.all(updatePromises);
        useImageStore.temporal.getState().clear();
        console.log('✅ 图片背景状态恢复完成');
      } else {
        console.log('无需恢复图片背景状态');
      }
    } catch (error) {
      console.error('恢复图片背景状态失败:', error);
    }
  }, [isStorageInitialized, checkImageUrlValid, uploadedBackgroundImages]);

  // 背景图片加载逻辑
  useEffect(() => {
    if (!isStorageInitialized || initializationStatus !== 'success') return;

    console.log('触发背景图片加载...');
    loadBackgroundImages();
  }, [isStorageInitialized, initializationStatus, loadBackgroundImages]);

  // 图片状态恢复逻辑 - 在背景图片加载完成后执行
  useEffect(() => {
    if (!isStorageInitialized || initializationStatus !== 'success') return;
    if (images.size === 0) return; // 没有图片时不需要恢复
    if (isLoadingBackgroundImages) return; // 等待背景图片加载完成

    let isCancelled = false;

    const restoreStates = async () => {
      try {
        console.log('背景图片加载完成，开始恢复图片背景状态...');

        if (isCancelled) return;

        await restoreImageBackgroundStates();

        if (isCancelled) return;

        // 清空历史记录
        useImageStore.temporal.getState().clear();
        console.log('✅ 图片状态恢复完成');
      } catch (error) {
        console.error('恢复图片状态时出错:', error);
      }
    };

    // 短暂延迟确保背景图片状态已更新
    const timer = setTimeout(() => {
      if (!isCancelled) {
        restoreStates();
      }
    }, 100);

    return () => {
      isCancelled = true;
      clearTimeout(timer);
    };
  }, [
    isStorageInitialized,
    initializationStatus,
    images.size,
    isLoadingBackgroundImages,
    uploadedBackgroundImages.length, // 依赖背景图片数量变化
    restoreImageBackgroundStates,
  ]);

  // 保存自定义背景图片
  const saveBackgroundImage = async (image: UploadedImage) => {
    if (!isStorageInitialized || !image.file) return;

    try {
      await imageStorage.saveBackgroundImage(image.id, image.file);
      console.log(`背景图片已保存: ${image.id}`);
    } catch (error) {
      console.error(`保存背景图片失败: ${image.id}`, error);
    }
  };

  // 删除自定义背景图片
  const deleteBackgroundImage = async (imageId: string) => {
    if (!isStorageInitialized) return;

    try {
      // 先找到要删除的图片
      let imageToDelete: UploadedImage | undefined;

      setUploadedBackgroundImages(prev => {
        imageToDelete = prev.find(img => img.id === imageId);
        return prev.filter(img => img.id !== imageId);
      });

      // 清理 URL
      if (imageToDelete?.url.startsWith('blob:')) {
        URL.revokeObjectURL(imageToDelete.url);
      }

      // 从 IndexedDB 中删除
      await imageStorage.deleteBackgroundImage(imageId);
    } catch (error) {
      console.error(`删除背景图片失败: ${imageId}`, error);
      // 如果删除失败，重新加载以恢复状态
      loadBackgroundImages();
    }
  };

  // 添加新的背景图片
  const addBackgroundImage = async (file: File) => {
    const newImage: UploadedImage = {
      id: `bgimg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      url: URL.createObjectURL(file),
      name: file.name,
      timestamp: Date.now(),
      file: file,
    };

    // 立即更新状态
    setUploadedBackgroundImages(prev => [newImage, ...prev]);

    // 异步保存到 IndexedDB
    if (isStorageInitialized) {
      try {
        await saveBackgroundImage(newImage);
      } catch (error) {
        console.error('保存背景图片失败:', error);
        // 如果保存失败，从状态中移除
        setUploadedBackgroundImages(prev =>
          prev.filter(img => img.id !== newImage.id)
        );
        URL.revokeObjectURL(newImage.url);
      }
    }

    return newImage;
  };

  return {
    // 初始化状态
    initializationStatus,
    initializationError,
    isStorageInitialized,

    // 存储状态
    isStorageLoading,
    isSaving,
    autoSaveEnabled,

    // 操作方法
    initializeAndLoadStorage,
    saveAllImages,
    cleanupStorage,
    toggleAutoSave,

    // 背景图片相关
    uploadedBackgroundImages,
    isLoadingBackgroundImages,
    addBackgroundImage,
    deleteBackgroundImage,
    loadBackgroundImages,
  };
};
