'use client';

import { MobileBackgroundRemover } from '@/components/mobile-background-remover';
import Image from 'next/image';

/**
 * 移动端专用的页面组件
 * 提供适合移动设备的用户界面和交互体验
 */
export function MobilePage() {
  return (
    <div className='flex flex-col h-full bg-white overflow-hidden'>
      {/* 移动端页眉 - 更紧凑的设计 */}
      <header className='border-b border-gray-200 bg-white px-4 py-3'>
        <div className='flex justify-between items-center'>
          {/* Logo - 移动端更小尺寸 */}
          <div className='flex items-center'>
            <Image src='/logo.png' width={80} height={20} alt='logo' />
          </div>

          {/* 移动端菜单按钮 */}
          <div className='flex items-center space-x-2'>
            <button className='h-8 px-3 border border-[#e7e7e7] rounded-md flex items-center justify-center text-sm font-medium text-[#121212]'>
              登录
            </button>
            <button className='h-8 px-3 bg-[#ffcc03] rounded-md flex items-center justify-center text-sm font-medium text-[#121212]'>
              注册
            </button>
          </div>
        </div>

        {/* 移动端标题 */}
        <div className='mt-3'>
          <h1 className='text-lg font-semibold text-[#121212]'>Remove BG</h1>
          <p className='text-sm text-gray-600 mt-1'>一键去除图片背景</p>
        </div>
      </header>

      {/* 移动端主要内容 */}
      <main className='flex-1 overflow-hidden'>
        <MobileBackgroundRemover />
      </main>
    </div>
  );
}
