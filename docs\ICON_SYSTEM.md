# 图标系统使用指南

## 概述
项目使用统一的图标交互系统，所有图标都可以通过添加 `icon-interactive` 类来实现主题色变化效果。

## 使用方法

### 1. 基础图标组件模式
```tsx
import React from 'react';

const YourIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    width='16'
    height='16'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
  >
    {/* SVG路径 */}
  </svg>
);

export default YourIcon;
```

### 2. 在组件中使用
```tsx
import YourIcon from './ui/your-icon';

// 基础使用
<YourIcon className="icon-interactive" />

// 在容器中使用（容器hover触发）
<div className="interactive-container">
  <YourIcon className="icon-interactive" />
  <span>文本</span>
</div>

// 禁用状态
<YourIcon className="icon-interactive disabled" />
<button disabled>
  <YourIcon className="icon-interactive" />
</button>
```

### 3. CSS类说明

- `.icon-interactive`: 应用到图标上，提供基础样式和交互效果
- `.interactive-container`: 应用到容器上，使容器hover时触发子图标变色
- `.disabled`: 手动应用禁用样式

### 4. 颜色状态

- **默认颜色**: `var(--text-primary)` (#121212)
- **Hover颜色**: `var(--brand-primary)` (#ffcc03)
- **禁用颜色**: `var(--brand-primary-disabled)` (#FFF7C7)
- **过渡时间**: `0.2s ease-in-out`

### 5. 触发方式

1. **直接hover图标**:
```tsx
<YourIcon className="icon-interactive" />
```

2. **父容器hover触发**:
```tsx
<div className="interactive-container">
  <YourIcon className="icon-interactive" />
</div>
```

3. **禁用状态**:
```tsx
<!-- 自动禁用 -->
<button disabled>
  <YourIcon className="icon-interactive" />
</button>

<!-- 手动禁用 -->
<YourIcon className="icon-interactive disabled" />
```

## 主题色系统

项目采用统一的主题色变量：

```css
/* 品牌主题色 */
--brand-primary: #ffcc03;           /* 主色 */
--brand-primary-hover: #FFE343;     /* 悬停色 */
--brand-primary-disabled: #FFF7C7;  /* 禁用色 */
```

## 最佳实践

1. 所有新建图标组件都应该支持 `className` prop
2. 默认导出使用 `export default` 模式
3. 图标尺寸通过width/height props或CSS控制
4. 保持SVG结构简洁，颜色通过CSS控制
5. 使用CSS变量而不是硬编码颜色值

## 示例

### 上传图标
```tsx
<div className="interactive-container">
  <UploadIcon className="icon-interactive" />
  <span>Upload Image</span>
</div>
```

### 删除图标（禁用状态）
```tsx
<button disabled>
  <DeleteIcon className="icon-interactive" />
</button>
```

### 设置图标
```tsx
<div className="interactive-container">
  <SettingsIcon className="icon-interactive" />
  <span>Settings</span>
</div>
``` 