'use client';

import { Button } from '@/components/ui/Button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';
import { X } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * 背景颜色选择器组件的 Props。
 */
interface BackgroundColorPickerProps {
  currentColor: string;
  onChangeColor: (color: string) => void;
  children: React.ReactNode;
}

// 预设的颜色列表
export const PREDEFINED_COLORS = [
  'transparent',
  '#000000',
  '#FFFFFF',
  '#E7E7E7',
  '#878787',
  '#FF3B30',
  '#FF2D55',
  '#AF52DE',
  '#5856D6',
  '#007AFF',
  '#32ADE6',
  '#64D2FF',
  '#00C7BE',
  '#00B06B',
  '#96D035',
  '#C2E62C',
  '#FFD60A',
  '#FFC300',
  '#FF9500',
  '#FF6B00',
  '#FF3B2F',
];

/**
 * 背景颜色选择器组件
 */
export function BackgroundColorPicker({
  currentColor,
  onChangeColor,
  children,
}: BackgroundColorPickerProps) {
  const [isCustomColorActive, setIsCustomColorActive] = useState(() => {
    return !PREDEFINED_COLORS.includes(currentColor);
  });
  const [open, setOpen] = useState(false);

  const colorInputRef = useRef<HTMLInputElement>(null);
  const throttleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!PREDEFINED_COLORS.includes(currentColor)) {
      setIsCustomColorActive(true);
    } else {
      setIsCustomColorActive(false);
    }
  }, [currentColor]);

  const handleColorChange = useCallback(
    (newColor: string, fromCustomPicker = false) => {
      onChangeColor(newColor);
      setIsCustomColorActive(fromCustomPicker);
    },
    [onChangeColor]
  );

  // 节流处理颜色变化，避免拖动时卡顿
  const throttledColorChange = useCallback(
    (newColor: string) => {
      if (throttleTimeoutRef.current) {
        clearTimeout(throttleTimeoutRef.current);
      }

      throttleTimeoutRef.current = setTimeout(() => {
        handleColorChange(newColor, true);
      }, 50); // 50ms 延迟
    },
    [handleColorChange]
  );

  const handleColorInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newColor = event.target.value;
    throttledColorChange(newColor);
  };

  const handleColorInputClick = () => {
    // 点击input时立即激活自定义颜色状态
    setIsCustomColorActive(true);
  };

  const handleClosePopover = () => {
    setOpen(false);
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>{children}</PopoverTrigger>
        <PopoverContent
          className='w-86 h-107 p-4 rounded-2xl border border-[#E7E7E7] shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)]'
          sideOffset={-50}
        >
          <div className='flex justify-between items-center mb-4'>
            <h3 className='text-base font-bold text-text-primary '>
              Change Background Colors
            </h3>
            {/* 右上角关闭按钮 */}
            <button
              onClick={handleClosePopover}
              className='w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors cursor-pointer'
            >
              <X className='w-6 h-6 text-text-primary' strokeWidth={1.5} />
            </button>
          </div>

          <div className='h-px bg-[#E7E7E7] -mx-4 mb-4' />

          <div className='space-y-4'>
            <div className='space-y-2'>
              <p className='text-[14px] font-medium text-[#878787] '>
                Custom Color
              </p>
              <div className='flex items-start justify-start relative'>
                {isCustomColorActive ? (
                  <Image
                    src='/apps/icons/customColorActive.svg'
                    alt='customColorActive'
                    width={32}
                    height={32}
                  />
                ) : (
                  <Image
                    src='/apps/icons/customColor.svg'
                    alt='customColor'
                    width={32}
                    height={32}
                  />
                )}
                <input
                  ref={colorInputRef}
                  type='color'
                  value={
                    currentColor === 'transparent' ? '#FFFFFF' : currentColor
                  }
                  onChange={handleColorInputChange}
                  onClick={handleColorInputClick}
                  style={{
                    display: 'block',
                    opacity: 0,
                    width: '32px',
                    height: '32px',
                    border: 'none',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                  }}
                />
              </div>
            </div>

            <div className='space-y-2'>
              <p className='text-[14px] font-medium text-[#878787] '>
                Preset Color
              </p>
              <div className='grid grid-cols-8 gap-2'>
                {PREDEFINED_COLORS.map(color => (
                  <Button
                    key={color}
                    variant='outline'
                    className={`w-8 h-8 p-0 rounded-full hover:scale-110 transition-transform ${
                      currentColor === color && !isCustomColorActive
                        ? 'ring-2 ring-offset-2 ring-[#FFCC03]'
                        : ''
                    } ${color === 'transparent' ? 'checkerboard' : ''}`}
                    style={{
                      backgroundColor:
                        color === 'transparent' ? undefined : color,
                      border: '1px solid rgba(18, 18, 18, 0.10)',
                    }}
                    onClick={() => handleColorChange(color, false)}
                    title={color === 'transparent' ? '透明' : color}
                  >
                    {color === 'transparent' && (
                      <div className='w-full h-full flex items-center justify-center rounded-full'></div>
                    )}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}
