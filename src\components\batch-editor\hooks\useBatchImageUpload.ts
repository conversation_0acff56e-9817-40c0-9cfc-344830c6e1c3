import { useCallback, useState } from 'react';
import { useDropzone, type FileRejection } from 'react-dropzone';
import { removeBackground } from '@/lib/api';
import { useImageStore } from '@/lib/store/imageStore';
import { useTips } from '@/components/ui/Tips';
import {
  validateImageFormat,
  createImageFromSource,
} from '../../background-remover/utils';
import { MAX_IMAGES_LIMIT } from '../../background-remover/constants';

// 非会员批量处理限制
const FREE_USER_BATCH_LIMIT = 3;

/**
 * 批量编辑专用的图片上传hook
 * 与普通上传的区别：上传后不自动去除背景，保持original状态
 * 非会员用户超过限制的图片会被设置为locked状态
 * 支持IndexedDB自动保存功能
 */
export const useBatchImageUpload = (isVipUser: boolean = false) => {
  const [isLoadingApi, setIsLoadingApi] = useState(false);
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);
  const [processingImageIds, setProcessingImageIds] = useState<Set<string>>(
    new Set()
  );

  const { showTips } = useTips();

  /**
   * 添加图片到处理状态
   */
  const addProcessingImages = useCallback((imageIds: string[]) => {
    setProcessingImageIds(prev => {
      const newSet = new Set(prev);
      imageIds.forEach(id => newSet.add(id));
      return newSet;
    });
  }, []);

  /**
   * 从处理状态中移除图片
   */
  const removeProcessingImages = useCallback((imageIds: string[]) => {
    setProcessingImageIds(prev => {
      const newSet = new Set(prev);
      imageIds.forEach(id => newSet.delete(id));
      return newSet;
    });
  }, []);

  /**
   * 清空所有处理状态
   */
  const clearProcessingImages = useCallback(() => {
    setProcessingImageIds(new Set());
  }, []);

  /**
   * 更新图片锁定状态 - 保持最新的3张图片为可处理状态
   * 非会员用户：最新的3张图片保持original状态，其余设为locked
   * 会员用户：所有图片保持original状态
   */
  const updateImageLockStatus = useCallback(() => {
    if (isVipUser) return; // 会员用户不需要锁定

    const imageStore = useImageStore.getState();
    const allImages = Array.from(imageStore.images.values());

    // 按上传时间排序，最新的在前
    const sortedImages = allImages.sort((a, b) => b.timestamp - a.timestamp);

    // 更新每张图片的状态
    sortedImages.forEach((image, index) => {
      const shouldBeUnlocked = index < FREE_USER_BATCH_LIMIT;
      const currentStatus = image.status;

      if (shouldBeUnlocked && currentStatus === 'locked') {
        // 解锁：从locked变为original
        imageStore.updateImage(image.id, { status: 'original' });
        console.log(`图片 ${image.name} 解锁为 original 状态`);
      } else if (!shouldBeUnlocked && currentStatus === 'original') {
        // 锁定：从original变为locked
        imageStore.updateImage(image.id, { status: 'locked' });
        console.log(`图片 ${image.name} 锁定为 locked 状态`);
      }
    });
  }, [isVipUser]);

  /**
   * 批量去除背景 - 仅处理选中的图片
   */
  const batchRemoveBackground = useCallback(
    async (imageIds: string[]) => {
      if (imageIds.length === 0) return;

      setIsLoadingApi(true);

      // 添加所有要处理的图片ID到处理列表
      setProcessingImageIds(prev => {
        const newSet = new Set(prev);
        imageIds.forEach(id => newSet.add(id));
        return newSet;
      });

      try {
        // 并发处理所有图片
        const promises = imageIds.map(async imageId => {
          const image = useImageStore.getState().images.get(imageId);
          if (!image || image.processedUrl || image.status === 'locked') {
            return; // 跳过已处理的图片和锁定的图片
          }

          try {
            // 将文件转换为 Data URL
            let imageDataUrl: string;
            if (image.file) {
              imageDataUrl = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(image.file!);
              });
            } else {
              imageDataUrl = image.previewUrl;
            }

            // 调用背景去除API
            const imageBlob = await removeBackground({ image: imageDataUrl });
            const objectURL = URL.createObjectURL(imageBlob);

            // 更新图片状态
            useImageStore.getState().updateImage(imageId, {
              processedUrl: objectURL,
              status: 'bg-removed',
            });

            console.log(`批量背景去除完成: ${imageId}`);
          } catch (error) {
            console.error(`批量处理图片背景去除失败: ${imageId}`, error);
            showTips('error', `Failed to process image: ${image.name}`, 3000);
          } finally {
            // 从处理列表中移除
            setProcessingImageIds(prev => {
              const newSet = new Set(prev);
              newSet.delete(imageId);
              return newSet;
            });
          }
        });

        await Promise.all(promises);
        showTips(
          'success',
          `Successfully processed ${imageIds.length} images`,
          3000
        );
      } catch (error) {
        console.error('批量处理失败:', error);
        showTips('error', 'Batch processing failed', 3000);
      } finally {
        setIsLoadingApi(false);
      }
    },
    [showTips]
  );

  /**
   * 处理文件拖拽上传 - 不自动去除背景
   */
  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: FileRejection[]) => {
      // 处理被拒绝的文件
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            showTips('error', `File ${file.name} is too large`, 3000);
          } else if (error.code === 'invalid-file-type') {
            showTips('error', `File ${file.name} format not supported`, 3000);
          }
        });
      });

      if (acceptedFiles.length === 0) return;

      // 检查图片数量限制
      const currentImages = useImageStore.getState().images;
      const currentCount = currentImages.size;
      const totalCount = currentCount + acceptedFiles.length;
      if (totalCount > MAX_IMAGES_LIMIT) {
        showTips(
          'error',
          `Maximum ${MAX_IMAGES_LIMIT} images allowed. Current: ${currentCount}`,
          4000
        );
        return;
      }

      const validFiles = acceptedFiles.filter(file => {
        const validation = validateImageFormat(file);
        if (!validation.isValid) {
          showTips(
            'error',
            `${file.name}: ${validation.error || 'Invalid file'}`,
            3000
          );
          return false;
        }
        return true;
      });

      if (validFiles.length === 0) return;

      setIsLoadingApi(true);

      const processAllFiles = async () => {
        // 暂停历史记录跟踪，因为这是初始化过程
        useImageStore.temporal.getState().pause();

        for (let i = 0; i < validFiles.length; i++) {
          const file = validFiles[i];
          try {
            const newImage = await createImageFromSource(file);
            useImageStore.getState().addImage(newImage);

            // 第一张图片立即选中
            if (i === 0) {
              useImageStore.getState().clearSelection();
              useImageStore.getState().toggleImageSelection(newImage.id);
            }

            console.log(`图片 ${file.name} 已上传，等待状态更新`);
          } catch (error) {
            console.error('Error processing file:', error);
            showTips('error', 'Failed to process uploaded file', 3000);
          }
        }

        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();

        // 更新图片锁定状态（保持最新3张可用）
        updateImageLockStatus();

        setIsLoadingApi(false);
      };

      processAllFiles();
    },
    [showTips, updateImageLockStatus]
  );

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
    },
    multiple: true,
    disabled: isLoadingApi,
    noClick: true,
    validator: file => {
      const validation = validateImageFormat(file);
      if (!validation.isValid) {
        if (validation.error === 'file-too-large') {
          return {
            code: 'file-too-large',
            message: 'Image file is too large',
          };
        } else {
          return {
            code: 'invalid-file-type',
            message: 'Only JPG, JPEG, PNG, and WebP formats are supported',
          };
        }
      }
      return null;
    },
  });

  /**
   * 从URL加载图片 - 不自动去除背景
   */
  const handleLoadFromUrl = useCallback(
    async (url: string) => {
      if (!url.trim()) return;

      setIsLoadingUrl(true);
      setIsLoadingApi(true);

      // 暂停历史记录跟踪，因为这是初始化过程
      useImageStore.temporal.getState().pause();

      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        if (!blob.type.startsWith('image/')) {
          throw new Error('URL does not point to a valid image');
        }

        // 从URL创建文件名
        const urlPath = new URL(url).pathname;
        const fileName = urlPath.split('/').pop() || 'image-from-url.png';
        const file = new File([blob], fileName, { type: blob.type });

        const newImage = await createImageFromSource(file);
        useImageStore.getState().addImage(newImage);

        // 默认选中新添加的图片
        useImageStore.getState().clearSelection();
        useImageStore.getState().toggleImageSelection(newImage.id);

        // 更新图片锁定状态（保持最新3张可用）
        updateImageLockStatus();

        console.log(`从URL加载的图片 ${fileName} 已上传，等待状态更新`);
      } catch (error) {
        console.error('从URL加载图片失败:', error);
        showTips(
          'error',
          'Unable to open image. The URL is invalid or wrong',
          3000
        );
      } finally {
        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingUrl(false);
        setIsLoadingApi(false);
      }
    },
    [showTips, updateImageLockStatus]
  );

  /**
   * 加载示例图片 - 不自动去除背景
   */
  const handleLoadSampleImage = useCallback(
    async (sampleUrl: string, sampleName: string) => {
      setIsLoadingApi(true);

      // 暂停历史记录跟踪，因为这是初始化过程
      useImageStore.temporal.getState().pause();

      try {
        // 获取示例图片数据并创建 File 对象
        const response = await fetch(sampleUrl);
        if (!response.ok) {
          throw new Error(`无法获取示例图片: ${response.statusText}`);
        }
        const blob = await response.blob();
        const file = new File([blob], sampleName, { type: blob.type });

        const newImage = await createImageFromSource(file);
        useImageStore.getState().addImage(newImage);

        // 默认选中新添加的图片
        useImageStore.getState().clearSelection();
        useImageStore.getState().toggleImageSelection(newImage.id);

        // 更新图片锁定状态（保持最新3张可用）
        updateImageLockStatus();

        console.log(`示例图片 ${sampleName} 已上传，等待状态更新`);
      } catch (error) {
        console.error('加载示例图片失败:', error);
        showTips('error', 'Failed to load sample image', 3000);
      } finally {
        // 初始化完成后恢复历史记录跟踪
        useImageStore.temporal.getState().resume();
        setIsLoadingApi(false);
      }
    },
    [showTips, updateImageLockStatus]
  );

  // 移除自动保存逻辑，避免无限循环
  // 批量编辑器不需要自动保存，用户操作时手动保存即可

  return {
    // 上传相关
    isDragActive,
    getInputProps,
    getRootProps,
    open,
    handleLoadFromUrl,
    handleLoadSampleImage,

    // 批量处理相关
    batchRemoveBackground,

    // 处理状态管理
    addProcessingImages,
    removeProcessingImages,
    clearProcessingImages,

    // 状态
    isLoadingApi,
    isLoadingUrl,
    processingImageIds,
  };
};
