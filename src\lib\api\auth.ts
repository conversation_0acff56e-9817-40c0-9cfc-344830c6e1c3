import { httpClient } from './http-client';

// 认证相关的接口类型定义
export interface LoginRequest {
  username: string;
  password: string;
  captcha?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken?: string;
  userInfo: UserInfo;
}

export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
}

export interface RegisterRequest {
  username: string;
  password: string;
  email?: string;
  captcha?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken?: string;
}

// 认证相关 API 函数

/**
 * 用户登录
 */
export async function login(credentials: LoginRequest): Promise<LoginResponse> {
  const response = await httpClient.post<LoginResponse>(
    '/auth/login',
    credentials
  );

  // 登录成功后自动保存 token
  if (response.token) {
    httpClient.setToken(response.token);
  }

  return response;
}

/**
 * 用户注册
 */
export async function register(userData: RegisterRequest): Promise<void> {
  return httpClient.post('/auth/register', userData);
}

/**
 * 用户登出
 */
export async function logout(): Promise<void> {
  try {
    await httpClient.post('/auth/logout');
  } catch (error) {
    console.warn('登出接口调用失败:', error);
  } finally {
    // 无论接口是否成功，都清除本地 token
    httpClient.clearToken();
  }
}

/**
 * 获取当前用户信息
 */
export async function getUserInfo(): Promise<UserInfo> {
  return httpClient.get<UserInfo>('/auth/user');
}

/**
 * 刷新 token
 */
export async function refreshToken(
  refreshData: RefreshTokenRequest
): Promise<RefreshTokenResponse> {
  const response = await httpClient.post<RefreshTokenResponse>(
    '/auth/refresh',
    refreshData
  );

  // 更新 token
  if (response.token) {
    httpClient.setToken(response.token);
  }

  return response;
}

/**
 * 修改密码
 */
export async function changePassword(data: {
  oldPassword: string;
  newPassword: string;
}): Promise<void> {
  return httpClient.post('/auth/change-password', data);
}

/**
 * 忘记密码 - 发送重置邮件
 */
export async function forgotPassword(email: string): Promise<void> {
  return httpClient.post('/auth/forgot-password', { email });
}

/**
 * 重置密码
 */
export async function resetPassword(data: {
  token: string;
  newPassword: string;
}): Promise<void> {
  return httpClient.post('/auth/reset-password', data);
}
