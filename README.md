# AI 背景去除工具

一个基于 Next.js 的现代化 AI 背景去除应用，使用 Photoroom API 提供专业级的背景移除功能。

## ✨ 功能特性

- 🎯 **专业级 AI 背景去除** - 集成 Photoroom API，提供工业级背景移除质量。
- 🚀 **生产就绪** - 直接调用真实 AI 服务，无模拟代码，开箱即用。
- 🎨 **强大的背景编辑** - 支持透明背景、纯色背景和自定义图片背景。
- 🖼️ **实时画布编辑** - 支持图像的缩放、平移等交互功能。
- 模糊效果 - 支持对背景进行高斯模糊处理。
- 🔄 **流畅的用户体验** - 上传图片后自动开始处理，支持拖拽上传和历史记录。
- 📱 **响应式设计** - 完美适配桌面和移动设备。
- 🎨 **现代化的 UI** - 基于 shadcn/ui 和 Tailwind CSS 的精美界面。

## 🛠️ 技术栈

- **框架**: Next.js (App Router)
- **语言**: TypeScript
- **UI 库**: shadcn/ui
- **CSS 框架**: Tailwind CSS
- **AI 服务**: PhotoRoom API
- **图标**: Lucide React
- **文件上传**: react-dropzone

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置 Photoroom API（必需）

**🔴 重要：应用需要有效的 Photoroom API 密钥才能运行**

1.  **获取 API 密钥**：访问 [Photoroom Developer Portal](https://www.photoroom.com/api/)
2.  在项目根目录创建 `.env.local` 文件并添加你的密钥：
    ```bash
    PHOTOROOM_API_KEY=your_actual_api_key_here
    ```
3.  详细配置说明请参考 [PHOTOROOM_SETUP.md](./PHOTOROOM_SETUP.md)

### 3. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 开始使用。

## 📁 项目结构

```
src/
├── app/
│   ├── api/
│   │   └── remove-background/
│   │       └── route.ts              # 处理背景去除的 API 路由
│   ├── globals.css                   # 全局样式和 CSS 变量
│   ├── layout.tsx                    # 应用根布局
│   └── page.tsx                      # 应用主页
├── components/
│   ├── ui/                           # shadcn/ui 自动生成的组件
│   ├── background-remover.tsx        # 主要的功能组件，整合所有功能
│   ├── canvas-image-editor.tsx       # 核心的 Canvas 编辑器
│   ├── starry-night-loading.tsx      # 加载动画
│   ├── error-boundary.tsx            # 错误边界组件
│   ├── background-image-picker.tsx   # 背景图片选择器
│   └── background-color-picker.tsx   # 背景颜色选择器
└── lib/
    └── utils.ts                    # 工具函数 (例如 cn)
```

## 🧩 主要组件

### BackgroundRemover

主要的背景去除组件，整合了以下功能：

- 文件上传（支持拖拽）
- 图像编辑区域
- 历史记录管理
- 侧边编辑面板（背景、颜色、模糊等）
- 底部工具栏（缩放、重置、对比、撤销/重做）

### CanvasImageEditor

核心的 Canvas 编辑器，负责：

- 渲染背景层（透明、纯色、图片）
- 渲染处理后的图像
- 处理图像的缩放和平移
- 实现原图对比功能

## 生产部署

### 环境变量配置

生产环境必须配置 `.env.local` 或相应的服务环境变量：

```env
PHOTOROOM_API_KEY=your_production_api_key_here
```

