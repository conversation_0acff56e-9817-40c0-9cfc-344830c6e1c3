'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import BackgroundRemoveIcon from '@/components/icons/BackgroundRemove';
import ResizeIcon from '@/components/icons/Resize';
import RenameIcon from '@/components/icons/Rename';
import ConvertIcon from '@/components/icons/Convert';
import CompressIcon from '@/components/icons/Compress';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'background',
    label: 'Background',
    icon: BackgroundRemoveIcon,
  },
  {
    id: 'resize',
    label: 'Resize',
    icon: ResizeIcon,
  },
  {
    id: 'rename',
    label: 'Rename',
    icon: RenameIcon,
  },
  {
    id: 'convert',
    label: 'Convert',
    icon: ConvertIcon,
  },
  {
    id: 'compress',
    label: 'Compress',
    icon: CompressIcon,
  },
];

interface SideNavigationProps {
  activeItem?: string;
  onItemChange?: (itemId: string) => void;
}

export function SideNavigation({
  activeItem = 'background',
  onItemChange,
}: SideNavigationProps) {
  const [selectedItem, setSelectedItem] = useState(activeItem);

  const handleItemClick = (itemId: string) => {
    setSelectedItem(itemId);
    onItemChange?.(itemId);
  };

  return (
    <div className='w-[120px] bg-white border-r border-border-divider flex flex-col gap-1 py-6 px-2 h-full text-center items-center'>
      {navigationItems.map(item => {
        const Icon = item.icon;
        const isActive = selectedItem === item.id;

        return (
          <button
            key={item.id}
            onClick={() => handleItemClick(item.id)}
            className={cn(
              'flex flex-col justify-center items-center gap-1 h-18 w-18 rounded-xl transition-all duration-200 interactive-container text-black hover:text-brand-primary ',
              'hover:bg-bg-light',
              isActive && 'bg-border'
            )}
          >
            <Icon
              size={24}
              className={cn('transition-colors duration-200 icon-interactive')}
            />
            <span className='text-[10px] font-medium transition-colors duration-200'>
              {item.label}
            </span>
          </button>
        );
      })}
    </div>
  );
}
