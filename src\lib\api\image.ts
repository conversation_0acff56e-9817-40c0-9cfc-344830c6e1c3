import { httpClient } from './http-client';

// 图像处理相关的接口类型定义
export interface RemoveBackgroundRequest {
  image: string; // base64 格式的图片，包含 data:image/[format];base64, 前缀（支持 png、jpg、jpeg、webp 等）
}

export interface RemoveBackgroundResponse {
  processedImage: string; // 处理后的图片二进制数据（base64编码）
}

// 去除背景相关 API 函数

/**
 * 去除图片背景 - 直接调用后端接口
 *
 * 接口信息：
 * - 路径：POST /api/v1/common/segment
 * - 参数：file (文件类型，图片二进制)
 * - 返回：{ code?: number, data?: string, msg?: string }
 * - data 字段包含图片二进制数据
 */
export async function removeBackground(
  request: RemoveBackgroundRequest
): Promise<Blob> {
  // 检查图片格式
  if (!request.image || !request.image.startsWith('data:image/')) {
    throw new Error('无效的图片格式，请提供有效的 base64 图片数据');
  }

  // 检测图片格式
  const mimeType = request.image.split(';')[0].split(':')[1] || 'image/png';
  const fileExtension = mimeType.split('/')[1] || 'png';

  const base64Data = request.image.split(',')[1];
  if (!base64Data) {
    throw new Error('无效的 Base64 图片数据');
  }

  // 将 base64 转换为 Blob 用于文件上传
  const imageBuffer = Buffer.from(base64Data, 'base64');
  const blob = new Blob([imageBuffer], { type: mimeType });

  // 创建 FormData
  const formData = new FormData();
  formData.append('file', blob, `image.${fileExtension}`);

  try {
    // 使用 httpClient 的上传方法，后端返回的是包含 base64 数据的 JSON
    const responseData = await httpClient.upload<string>(
      '/api/v1/common/segment',
      formData
    );

    // 将返回的 base64 数据转换为 Blob
    let base64Part: string;
    if (responseData.includes(',')) {
      // 如果包含 data URL 前缀，去除前缀
      base64Part = responseData.split(',')[1];
    } else {
      // 直接是 base64 数据
      base64Part = responseData;
    }

    // 将 base64 转换为 Uint8Array
    const binaryString = atob(base64Part);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    return new Blob([bytes], { type: mimeType });
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('网络请求失败，请稍后重试');
  }
}

/**
 * 去除图片背景 - 使用 JSON 参数（备用方案）
 *
 * 注意：根据接口文档，正式接口只接受 multipart/form-data 格式的 file 参数
 * 这个方法仅作为备用，如果后端同时支持 JSON 格式的话
 */
export async function removeBackgroundWithBase64(
  request: RemoveBackgroundRequest
): Promise<RemoveBackgroundResponse> {
  return httpClient.post<RemoveBackgroundResponse>('/api/v1/common/segment', {
    image: request.image,
  });
}
