'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useImageStore } from '@/lib/store/imageStore';
import {
  downloadSingleImage,
  downloadImagesAsZip,
  getImageDownloadInfo,
} from '@/lib/utils/imageDownload';
import type { SupportedFormat } from '@/lib/utils/imageConvert';
import { Button } from '../ui/Button';

/**
 * 安全地将字符串转换为支持的格式
 */
function toSupportedFormat(format: string): SupportedFormat {
  const supportedFormats: SupportedFormat[] = [
    'png',
    'jpg',
    'jpeg',
    'webp',
    'bmp',
    'xbm',
    'xpm',
  ];
  return supportedFormats.includes(format as SupportedFormat)
    ? (format as SupportedFormat)
    : 'png';
}

interface HeaderProps {
  className?: string;
  variant?: 'desktop' | 'batch-editor';
}

/**
 * 通用头部组件
 * 支持桌面端和批量编辑器两种变体
 */
export function Header({ className, variant }: HeaderProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  // 获取未锁定的图片数量，排除被锁定的图片
  const imageCount = useImageStore(
    state =>
      Array.from(state.images.values()).filter(
        image => image.status !== 'locked'
      ).length
  );

  // 在下载时才获取具体的图片数据，过滤掉锁定的图片
  const getImages = useCallback(() => {
    return Array.from(useImageStore.getState().images.values()).filter(
      image => image.status !== 'locked'
    );
  }, []);

  const handleDownload = async () => {
    const images = getImages();

    if (images.length === 0) {
      alert('没有可下载的图片');
      return;
    }

    setIsDownloading(true);

    try {
      if (images.length === 1) {
        // 单张图片直接下载
        const image = images[0];
        const downloadInfo = await getImageDownloadInfo(image);

        await downloadSingleImage(
          downloadInfo.url,
          downloadInfo.fileName,
          toSupportedFormat(downloadInfo.format)
        );

        console.log('单张图片下载完成');
      } else {
        // 多张图片打包下载 - 需要并发处理所有图片的合成
        const downloadImages = await Promise.all(
          images.map(async image => {
            const downloadInfo = await getImageDownloadInfo(image);
            return {
              url: downloadInfo.url,
              name: downloadInfo.fileName,
              format: toSupportedFormat(downloadInfo.format),
            };
          })
        );

        await downloadImagesAsZip(
          downloadImages,
          `pixpretty_images_${new Date().toISOString().slice(0, 10)}`
        );

        console.log('批量下载完成');
      }
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请重试');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <header className={cn('border-b border-gray-200 bg-white', className)}>
      <div className='mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          {/* Logo */}
          <div className='flex items-center'>
            <div className='flex items-center space-x-6'>
              {/* PixPretty Logo */}
              <div className='flex items-center space-x-2'>
                <Image
                  src='/apps/logo.png'
                  width={124}
                  height={32}
                  alt='logo'
                />
              </div>

              {/* 分隔线 */}
              <div className='bg-[#e7e7e7] h-[18px] w-px' />

              {/* Remove BG 下拉菜单 */}
              <div className='flex items-center space-x-2'>
                <span className='text-base font-medium text-text-primary'>
                  Remove BG
                </span>
                <div className='relative size-4'>
                  <Image
                    src='/apps/icons/down.svg'
                    width={16}
                    height={16}
                    alt='down'
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 用户操作按钮 */}
          <div className='flex items-center space-x-3'>
            {/* Download 按钮 */}
            {variant === 'batch-editor' && (
              <Button
                onClick={handleDownload}
                disabled={isDownloading || imageCount === 0}
                className={cn('h-9 px-4 ', {
                  'opacity-50 cursor-not-allowed':
                    isDownloading || imageCount === 0,
                  'min-w-[96px]': isDownloading,
                })}
              >
                <div className='flex items-center gap-2'>
                  <Image
                    src='/apps/icons/dowload.svg'
                    width={24}
                    height={24}
                    alt='dowload'
                  />
                  <span>Download</span>
                  {imageCount > 1 && <span>{imageCount}</span>}
                </div>
              </Button>
            )}

            <Button className='h-9' variant={'outline'}>
              Sign up
            </Button>
            <Button className='h-9 '>Sign in</Button>
          </div>
        </div>
      </div>
    </header>
  );
}
