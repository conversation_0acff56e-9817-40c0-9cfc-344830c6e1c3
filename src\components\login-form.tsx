'use client';

import { forgotPassword, login, type LoginRequest } from '@/lib/api';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { Input } from './ui/Input';
import { Label } from './ui/Label';

interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export function LoginForm({
  onSuccess,
  redirectTo = '/dashboard',
}: LoginFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
    captcha: '',
  });

  // 处理表单输入变化
  const handleInputChange =
    (field: keyof LoginRequest) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData(prev => ({
        ...prev,
        [field]: e.target.value,
      }));
      // 清除错误信息
      if (error) {
        setError(null);
      }
    };

  // 处理登录提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.username || !formData.password) {
      setError('请填写用户名和密码');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 直接调用登录函数
      const response = await login(formData);

      console.log('登录成功:', response);

      // 如果你需要将 token 保存到 cookie（用于 SSR）
      if (typeof window !== 'undefined') {
        document.cookie = `token=${response.token}; path=/; max-age=${7 * 24 * 60 * 60}`; // 7天
      }

      // 调用成功回调
      onSuccess?.();

      // 重定向到指定页面
      router.push(redirectTo);
    } catch (err) {
      console.error('登录失败:', err);
      setError(err instanceof Error ? err.message : '登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理忘记密码
  const handleForgotPassword = async () => {
    if (!formData.username) {
      setError('请先输入用户名');
      return;
    }

    try {
      setLoading(true);
      await forgotPassword(formData.username);
      alert('重置密码邮件已发送，请检查您的邮箱');
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送重置邮件失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className='w-full max-w-md mx-auto p-6'>
      <div className='space-y-6'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold'>登录</h1>
          <p className='text-gray-600'>请输入您的账号信息</p>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4'>
          {error && (
            <div className='p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md'>
              {error}
            </div>
          )}

          <div className='space-y-2'>
            <Label htmlFor='username'>用户名</Label>
            <Input
              id='username'
              type='text'
              value={formData.username}
              onChange={handleInputChange('username')}
              placeholder='请输入用户名'
              disabled={loading}
              required
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='password'>密码</Label>
            <Input
              id='password'
              type='password'
              value={formData.password}
              onChange={handleInputChange('password')}
              placeholder='请输入密码'
              disabled={loading}
              required
            />
          </div>

          {/* 如果需要验证码 */}
          <div className='space-y-2'>
            <Label htmlFor='captcha'>验证码（可选）</Label>
            <Input
              id='captcha'
              type='text'
              value={formData.captcha}
              onChange={handleInputChange('captcha')}
              placeholder='请输入验证码'
              disabled={loading}
            />
          </div>

          <Button type='submit' className='w-full' disabled={loading}>
            {loading ? '登录中...' : '登录'}
          </Button>

          <div className='flex justify-between text-sm'>
            <button
              type='button'
              onClick={handleForgotPassword}
              className='text-blue-600 hover:underline'
              disabled={loading}
            >
              忘记密码？
            </button>
            <button
              type='button'
              onClick={() => router.push('/register')}
              className='text-blue-600 hover:underline'
              disabled={loading}
            >
              注册账号
            </button>
          </div>
        </form>
      </div>
    </Card>
  );
}
