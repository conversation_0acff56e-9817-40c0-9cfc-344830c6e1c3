# Prettier 配置说明

本项目已经配置了 Prettier 代码格式化工具，与 ESLint 和 Husky 集成。

## 📁 配置文件

### `.prettierrc`
Prettier 的主要配置文件，包含以下设置：

```json
{
  "semi": true,                  // 使用分号
  "trailingComma": "es5",       // ES5 兼容的尾随逗号
  "singleQuote": true,          // 使用单引号
  "printWidth": 80,             // 行宽限制为 80 字符
  "tabWidth": 2,                // 缩进宽度为 2 空格
  "useTabs": false,             // 使用空格而不是制表符
  "bracketSpacing": true,       // 对象括号内加空格
  "bracketSameLine": false,     // JSX 标签的 > 换行
  "arrowParens": "avoid",       // 箭头函数参数避免括号
  "endOfLine": "auto",          // 自动检测行尾
  "jsxSingleQuote": true        // JSX 中使用单引号
}
```

### `.prettierignore`
指定 Prettier 应该忽略的文件和目录，包括：
- `node_modules/`
- `.next/`
- `build/`
- `*.md` (Markdown 文件)
- 配置文件等

## 🚀 NPM 脚本

在 `package.json` 中添加了以下脚本：

```bash
# 格式化所有文件
npm run format

# 检查格式化状态（不修改文件）
npm run format:check

# 修复 ESLint 错误
npm run lint:fix
```

## 🔧 集成配置

### ESLint 集成
- 安装了 `eslint-config-prettier` 避免与 ESLint 规则冲突
- 在 `eslint.config.mjs` 中扩展了 `"prettier"` 配置

### Husky 集成
- **pre-commit hook**: 使用 `lint-staged` 只对暂存文件进行格式化和 lint 检查
- 自动格式化代码并修复 ESLint 错误

### lint-staged 配置
```json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "prettier --write",
      "eslint --fix"
    ],
    "*.{json,css,md}": [
      "prettier --write"
    ]
  }
}
```

## 🎯 VS Code 配置

创建了 `.vscode/settings.json` 文件，包含：
- 默认使用 Prettier 作为格式化工具
- 保存时自动格式化
- 粘贴时自动格式化
- 保存时自动修复 ESLint 错误

## 📝 使用方法

### 1. 自动格式化（推荐）
通过 Git hooks 和 VS Code 设置，代码会自动格式化：
- **保存文件时**: VS Code 自动格式化
- **提交代码时**: Husky 自动格式化暂存文件

### 2. 手动格式化
```bash
# 格式化所有文件
npm run format

# 只检查格式，不修改
npm run format:check

# 格式化特定文件
npx prettier --write src/components/Button.tsx
```

### 3. 在编辑器中
- **VS Code**: 安装 "Prettier - Code formatter" 扩展
- **快捷键**: `Alt + Shift + F` (Windows) 或 `Option + Shift + F` (Mac)

## 🛠️ 自定义配置

如需修改 Prettier 规则，编辑 `.prettierrc` 文件：

```json
{
  "printWidth": 100,     // 增加行宽到 100
  "semi": false,         // 不使用分号
  "singleQuote": false   // 使用双引号
}
```

## 🔍 常见问题

### Q: 如何跳过某行的格式化？
A: 使用注释：
```javascript
// prettier-ignore
const uglyCode = { a:1,b:2,c:3 }
```

### Q: 如何跳过某个文件？
A: 将文件路径添加到 `.prettierignore`

### Q: ESLint 和 Prettier 冲突怎么办？
A: 已经配置了 `eslint-config-prettier` 来避免冲突

## 📖 相关链接

- [Prettier 官方文档](https://prettier.io/docs/en/)
- [配置选项](https://prettier.io/docs/en/options.html)
- [VS Code 扩展](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) 