'use client';

import { create } from 'zustand';

/**
 * 图片状态快照
 */
export interface ImageStateSnapshot {
  id: string;
  status:
    | 'original'
    | 'bg-removed'
    | 'compressed'
    | 'locked'
    | 'bg-remove-failed';
  backgroundColor?: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string;
  targetWidth?: number;
  targetHeight?: number;
  processedUrl?: string | null;
  compressedUrl?: string | null;
  compressedSize?: number;
  convertedFormat?: string;
  convertedUrl?: string | null;
}

/**
 * 批量操作历史记录项
 */
export interface BatchHistoryItem {
  id: string;
  timestamp: number;
  operation: 'background' | 'resize' | 'compress' | 'convert' | 'rename';
  description: string;
  imageStates: Record<string, ImageStateSnapshot>; // 使用Record代替Map，便于序列化
}

/**
 * 批量历史记录状态
 */
interface BatchHistoryState {
  // 历史记录列表
  history: BatchHistoryItem[];
  // 当前历史记录索引
  currentIndex: number;
  // 是否可以撤销
  canUndo: boolean;
  // 是否可以重做
  canRedo: boolean;

  // 操作方法
  addHistoryItem: (item: Omit<BatchHistoryItem, 'id' | 'timestamp'>) => void;
  undo: () => BatchHistoryItem | null;
  redo: () => BatchHistoryItem | null;
  clearHistory: () => void;
  getCurrentHistoryItem: () => BatchHistoryItem | null;
  getHistoryLength: () => number;
}

/**
 * 生成唯一ID
 */
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

/**
 * 创建批量历史记录store
 */
export const useBatchHistoryStore = create<BatchHistoryState>((set, get) => ({
  history: [],
  currentIndex: -1,
  canUndo: false,
  canRedo: false,

  /**
   * 添加新的历史记录项
   */
  addHistoryItem: (item: Omit<BatchHistoryItem, 'id' | 'timestamp'>) => {
    const state = get();
    const newItem: BatchHistoryItem = {
      ...item,
      id: generateId(),
      timestamp: Date.now(),
    };

    // 如果当前不在历史记录的末尾，需要删除当前位置之后的所有记录
    let newHistory = [...state.history];
    if (state.currentIndex < state.history.length - 1) {
      newHistory = newHistory.slice(0, state.currentIndex + 1);
    }

    // 添加新记录
    newHistory.push(newItem);

    // 限制历史记录数量，防止内存过度使用
    const MAX_HISTORY_SIZE = 50;
    if (newHistory.length > MAX_HISTORY_SIZE) {
      newHistory = newHistory.slice(-MAX_HISTORY_SIZE);
    }

    const newIndex = newHistory.length - 1;

    set({
      history: newHistory,
      currentIndex: newIndex,
      canUndo: newIndex > 0,
      canRedo: false, // 添加新记录后不能重做
    });

    console.log('批量历史记录已添加:', {
      operation: newItem.operation,
      description: newItem.description,
      currentIndex: newIndex,
      historyLength: newHistory.length,
    });
  },

  /**
   * 撤销操作
   */
  undo: () => {
    const state = get();
    if (!state.canUndo || state.currentIndex <= 0) {
      return null;
    }

    const newIndex = state.currentIndex - 1;
    const targetItem = state.history[newIndex];

    set({
      currentIndex: newIndex,
      canUndo: newIndex > 0,
      canRedo: true,
    });

    console.log('批量历史记录撤销:', {
      operation: targetItem.operation,
      description: targetItem.description,
      newIndex,
    });

    return targetItem;
  },

  /**
   * 重做操作
   */
  redo: () => {
    const state = get();
    if (!state.canRedo || state.currentIndex >= state.history.length - 1) {
      return null;
    }

    const newIndex = state.currentIndex + 1;
    const targetItem = state.history[newIndex];

    set({
      currentIndex: newIndex,
      canUndo: true,
      canRedo: newIndex < state.history.length - 1,
    });

    console.log('批量历史记录重做:', {
      operation: targetItem.operation,
      description: targetItem.description,
      newIndex,
    });

    return targetItem;
  },

  /**
   * 清空历史记录
   */
  clearHistory: () => {
    set({
      history: [],
      currentIndex: -1,
      canUndo: false,
      canRedo: false,
    });
    console.log('批量历史记录已清空');
  },

  /**
   * 获取当前历史记录项
   */
  getCurrentHistoryItem: () => {
    const state = get();
    if (state.currentIndex >= 0 && state.currentIndex < state.history.length) {
      return state.history[state.currentIndex];
    }
    return null;
  },

  /**
   * 获取历史记录长度
   */
  getHistoryLength: () => {
    return get().history.length;
  },
}));

/**
 * 便捷的历史记录管理函数
 */
export const batchHistoryActions = {
  addHistory: (item: Omit<BatchHistoryItem, 'id' | 'timestamp'>) => {
    useBatchHistoryStore.getState().addHistoryItem(item);
  },

  undo: () => {
    return useBatchHistoryStore.getState().undo();
  },

  redo: () => {
    return useBatchHistoryStore.getState().redo();
  },

  clear: () => {
    useBatchHistoryStore.getState().clearHistory();
  },

  canUndo: () => {
    return useBatchHistoryStore.getState().canUndo;
  },

  canRedo: () => {
    return useBatchHistoryStore.getState().canRedo;
  },
};
