'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { getDeviceType } from '../utils';

type DeviceType = 'mobile' | 'desktop';

interface DeviceContextType {
  deviceType: DeviceType;
  isMobile: boolean;
  isDesktop: boolean;
}

const DeviceContext = createContext<DeviceContextType | undefined>(undefined);

interface DeviceProviderProps {
  children: React.ReactNode;
  initialDeviceType?: DeviceType;
}

/**
 * 设备检测 Provider
 * 提供设备类型的上下文，支持服务端和客户端的设备检测
 */
export function DeviceProvider({
  children,
  initialDeviceType,
}: DeviceProviderProps) {
  const [deviceType, setDeviceType] = useState<DeviceType>(
    initialDeviceType || 'desktop'
  );

  useEffect(() => {
    // 客户端环境下重新检测设备类型
    const detectedDeviceType = getDeviceType();
    setDeviceType(detectedDeviceType);

    // 监听窗口大小变化，动态调整设备类型
    const handleResize = () => {
      const currentDeviceType = getDeviceType();
      setDeviceType(currentDeviceType);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const contextValue: DeviceContextType = {
    deviceType,
    isMobile: deviceType === 'mobile',
    isDesktop: deviceType === 'desktop',
  };

  return (
    <DeviceContext.Provider value={contextValue}>
      {children}
    </DeviceContext.Provider>
  );
}

/**
 * 使用设备检测的 Hook
 * @returns 设备类型相关的状态和方法
 */
export function useDevice() {
  const context = useContext(DeviceContext);
  if (context === undefined) {
    throw new Error('useDevice must be used within a DeviceProvider');
  }
  return context;
}
