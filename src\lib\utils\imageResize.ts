/**
 * 图片尺寸调整工具函数
 *
 * 支持多种调整模式：
 * - fit: 按比例缩放，保持宽高比，确保图片完全显示在目标尺寸内
 * - fill: 按比例缩放，保持宽高比，填满目标尺寸（可能裁剪）
 * - stretch: 直接拉伸到目标尺寸（可能变形）
 */

import {
  getFormatMimeType,
  type SupportedFormat,
} from '@/lib/utils/imageConvert';

export type ResizeMode = 'fit' | 'fill' | 'stretch';

/**
 * 计算调整后的尺寸信息
 */
export interface ResizeInfo {
  canvasWidth: number; // Canvas 画布宽度
  canvasHeight: number; // Canvas 画布高度
  drawX: number; // 图片绘制起始 X 坐标
  drawY: number; // 图片绘制起始 Y 坐标
  drawWidth: number; // 图片绘制宽度
  drawHeight: number; // 图片绘制高度
  sourceX?: number; // 源图片裁剪起始 X 坐标（fill 模式使用）
  sourceY?: number; // 源图片裁剪起始 Y 坐标（fill 模式使用）
  sourceWidth?: number; // 源图片裁剪宽度（fill 模式使用）
  sourceHeight?: number; // 源图片裁剪高度（fill 模式使用）
}

/**
 * 根据调整模式计算尺寸信息
 * @param originalWidth 原始图片宽度
 * @param originalHeight 原始图片高度
 * @param targetWidth 目标宽度
 * @param targetHeight 目标高度
 * @param mode 调整模式
 * @returns 调整信息
 */
export function calculateResizeInfo(
  originalWidth: number,
  originalHeight: number,
  targetWidth: number,
  targetHeight: number,
  mode: ResizeMode = 'fit'
): ResizeInfo {
  const info: ResizeInfo = {
    canvasWidth: targetWidth,
    canvasHeight: targetHeight,
    drawX: 0,
    drawY: 0,
    drawWidth: targetWidth,
    drawHeight: targetHeight,
  };

  switch (mode) {
    case 'fit': {
      // 按比例缩放，保持宽高比，确保图片完全显示
      const scaleX = targetWidth / originalWidth;
      const scaleY = targetHeight / originalHeight;
      const scale = Math.min(scaleX, scaleY);

      info.drawWidth = originalWidth * scale;
      info.drawHeight = originalHeight * scale;
      info.drawX = (targetWidth - info.drawWidth) / 2;
      info.drawY = (targetHeight - info.drawHeight) / 2;
      break;
    }

    case 'fill': {
      // 按比例缩放，保持宽高比，填满目标尺寸，可能裁剪
      const scaleX = targetWidth / originalWidth;
      const scaleY = targetHeight / originalHeight;
      const scale = Math.max(scaleX, scaleY);

      const scaledWidth = originalWidth * scale;
      const scaledHeight = originalHeight * scale;

      // 如果缩放后的图片比目标尺寸大，需要裁剪源图片
      if (scaledWidth > targetWidth || scaledHeight > targetHeight) {
        // 计算需要从源图片中裁剪的区域
        info.sourceWidth = targetWidth / scale;
        info.sourceHeight = targetHeight / scale;
        info.sourceX = (originalWidth - info.sourceWidth) / 2;
        info.sourceY = (originalHeight - info.sourceHeight) / 2;

        info.drawX = 0;
        info.drawY = 0;
        info.drawWidth = targetWidth;
        info.drawHeight = targetHeight;
      } else {
        // 缩放后的图片完全在目标尺寸内
        info.drawWidth = scaledWidth;
        info.drawHeight = scaledHeight;
        info.drawX = (targetWidth - scaledWidth) / 2;
        info.drawY = (targetHeight - scaledHeight) / 2;
      }
      break;
    }

    case 'stretch': {
      // 直接拉伸到目标尺寸
      info.drawX = 0;
      info.drawY = 0;
      info.drawWidth = targetWidth;
      info.drawHeight = targetHeight;
      break;
    }

    default:
      throw new Error(`不支持的调整模式: ${mode}`);
  }

  return info;
}

/**
 * 计算文件大小（估算）
 */
function estimateFileSize(dataUrl: string): number {
  // 移除data URL前缀
  const base64Data = dataUrl.split(',')[1];
  if (!base64Data) return 0;

  // Base64编码后的大小约为原始大小的4/3
  return Math.round((base64Data.length * 3) / 4);
}

/**
 * 使用 Canvas 调整图片尺寸
 * @param imageUrl 图片 URL 或 base64
 * @param targetWidth 目标宽度
 * @param targetHeight 目标高度
 * @param mode 调整模式
 * @param quality 输出质量 (0-1)，仅对 JPEG 有效
 * @param outputFormat 输出格式
 * @returns Promise<{dataUrl: string, size: number}> 调整后的图片数据和文件大小
 */
export async function resizeImage(
  imageUrl: string,
  targetWidth: number,
  targetHeight: number,
  mode: ResizeMode = 'fit',
  quality: number = 0.9,
  outputFormat: SupportedFormat = 'png'
): Promise<{ dataUrl: string; size: number }> {
  return new Promise((resolve, reject) => {
    try {
      // 创建图片对象
      const img = new Image();

      // 设置跨域属性（如果需要）
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        try {
          // 计算调整信息
          const resizeInfo = calculateResizeInfo(
            img.naturalWidth,
            img.naturalHeight,
            targetWidth,
            targetHeight,
            mode
          );

          // 创建 Canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            reject(new Error('无法创建 Canvas 2D 上下文'));
            return;
          }

          // 设置 Canvas 尺寸
          canvas.width = resizeInfo.canvasWidth;
          canvas.height = resizeInfo.canvasHeight;

          // 设置高质量渲染
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';

          // 获取MIME类型
          const mimeType = getFormatMimeType(outputFormat);

          // 填充背景（如果输出格式是 JPEG，需要白色背景）
          if (mimeType === 'image/jpeg') {
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
          }

          // 绘制图片
          if (
            resizeInfo.sourceX !== undefined &&
            resizeInfo.sourceY !== undefined &&
            resizeInfo.sourceWidth !== undefined &&
            resizeInfo.sourceHeight !== undefined
          ) {
            // fill 模式，需要裁剪源图片
            ctx.drawImage(
              img,
              resizeInfo.sourceX,
              resizeInfo.sourceY,
              resizeInfo.sourceWidth,
              resizeInfo.sourceHeight,
              resizeInfo.drawX,
              resizeInfo.drawY,
              resizeInfo.drawWidth,
              resizeInfo.drawHeight
            );
          } else {
            // fit 或 stretch 模式，绘制整个图片
            ctx.drawImage(
              img,
              resizeInfo.drawX,
              resizeInfo.drawY,
              resizeInfo.drawWidth,
              resizeInfo.drawHeight
            );
          }

          // 转换为 base64
          const resizedDataUrl = canvas.toDataURL(mimeType, quality);

          // 计算文件大小
          const size = estimateFileSize(resizedDataUrl);

          resolve({ dataUrl: resizedDataUrl, size });
        } catch (error) {
          reject(
            new Error(
              `图片处理失败: ${error instanceof Error ? error.message : String(error)}`
            )
          );
        }
      };

      img.onerror = () => {
        reject(new Error('图片加载失败'));
      };

      // 开始加载图片
      img.src = imageUrl;
    } catch (error) {
      reject(
        new Error(
          `初始化失败: ${error instanceof Error ? error.message : String(error)}`
        )
      );
    }
  });
}

/**
 * 批量调整图片尺寸
 * @param images 图片 URL 数组
 * @param targetWidth 目标宽度
 * @param targetHeight 目标高度
 * @param mode 调整模式
 * @param onProgress 进度回调
 * @param quality 输出质量
 * @param outputFormat 输出格式
 * @returns Promise<{dataUrl: string, size: number}[]> 调整后的图片数据和大小数组
 */
export async function resizeImagesBatch(
  images: string[],
  targetWidth: number,
  targetHeight: number,
  mode: ResizeMode = 'fit',
  onProgress?: (current: number, total: number, currentImage?: string) => void,
  quality: number = 0.9,
  outputFormat: SupportedFormat = 'png'
): Promise<{ dataUrl: string; size: number }[]> {
  const results: { dataUrl: string; size: number }[] = [];
  const total = images.length;

  for (let i = 0; i < total; i++) {
    const imageUrl = images[i];

    try {
      // 更新进度
      if (onProgress) {
        onProgress(i, total, imageUrl);
      }

      // 调整当前图片尺寸
      const resizeResult = await resizeImage(
        imageUrl,
        targetWidth,
        targetHeight,
        mode,
        quality,
        outputFormat
      );

      results.push(resizeResult);
    } catch (error) {
      console.error(`图片 ${i + 1} 处理失败:`, error);
      // 失败时推入空结果
      results.push({ dataUrl: '', size: 0 });
    }
  }

  // 最终进度更新
  if (onProgress) {
    onProgress(total, total);
  }

  return results;
}

/**
 * 获取图片的原始尺寸
 * @param imageUrl 图片 URL
 * @returns Promise<{width: number, height: number}> 图片尺寸
 */
export async function getImageDimensions(
  imageUrl: string
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    img.onerror = () => {
      reject(new Error('无法加载图片以获取尺寸'));
    };

    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
  });
}

/**
 * 计算合适的输出质量
 * 大图片使用较低质量以减少文件大小
 * @param width 图片宽度
 * @param height 图片高度
 * @returns 建议的质量值 (0-1)
 */
export function calculateOptimalQuality(width: number, height: number): number {
  const pixels = width * height;

  // 根据像素数量调整质量
  if (pixels < 500000) return 0.95; // 小图片：高质量
  if (pixels < 1000000) return 0.9; // 中等图片：较高质量
  if (pixels < 2000000) return 0.85; // 大图片：中等质量
  return 0.8; // 超大图片：较低质量
}
