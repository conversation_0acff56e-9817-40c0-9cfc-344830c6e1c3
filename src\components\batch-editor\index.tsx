'use client';

import React, { useState } from 'react';
import { SideNavigation } from './components/SideNavigation';
import { BackgroundSettings } from './components/BackgroundSettings';
import { ResizeSettings } from './components/ResizeSettings';
import { RenameSettings } from './components/RenameSettings';
import { ConvertSettings } from './components/ConvertSettings';
import { CompressSettings } from './components/CompressSettings';
import { ImageUploadGrid } from './components/ImageUploadArea';
import { Header } from '@/components/common/Header';
import { useBatchImageUpload } from './hooks/useBatchImageUpload';
import { useBatchImageStorage } from './hooks/useBatchImageStorage';
import { useImageEditor } from '../background-remover/hooks/useImageEditor';
import { useImageStore } from '@/lib/store/imageStore';
import { useBatchHistory } from './hooks/useBatchHistory';
import type { UploadedImage } from './components/BatchBackgroundImagePicker';

interface BackgroundSettings {
  type: 'color' | 'photos';
  color?: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string;
}

interface BatchEditorProps {
  className?: string;
  isVipUser?: boolean; // 是否为会员用户
}

interface BackgroundSettings {
  type: 'color' | 'photos';
  color?: string;
  backgroundImageUrl?: string;
}

interface ResizeSettings {
  width?: number;
  height?: number;
  aspectRatio?: number; // 宽高比 (width/height)
  scale?: number; // 缩放比例 (0.2-2.0)
  mode: 'fixed' | 'ratio' | 'scale' | 'custom'; // 固定尺寸、宽高比、缩放或自定义模式
  customMode?: 'scale' | 'dimensions'; // 自定义模式的子类型
  unit?: 'px' | 'in' | 'mm'; // 单位
  lockAspectRatio?: boolean; // 是否锁定宽高比
}

interface RenameSettings {
  prefix: string;
  startNumber: number;
  numberStep: number;
}

interface ConvertSettings {
  format: string;
}

interface CompressSettings {
  level?: 'original' | 'light' | 'medium' | 'deep';
  customSize?: number;
  customUnit?: 'KB' | 'MB';
}

/**
 * 批量编辑组件
 * 提供批量处理图片的功能，包括背景移除、尺寸调整、重命名、格式转换、压缩等
 */
export function BatchEditor({
  className,
  isVipUser = false,
}: BatchEditorProps) {
  const [activeFeature, setActiveFeature] = useState('background');
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);

  // 批量存储相关 hook
  const {
    initializationStatus,
    initializationError,
    uploadedBackgroundImages,
    addBackgroundImage,
    deleteBackgroundImage,
  } = useBatchImageStorage();

  // 调试：检查addBackgroundImage是否存在
  console.log(
    'BatchEditor: addBackgroundImage存在:',
    !!addBackgroundImage,
    typeof addBackgroundImage
  );

  // 批量上传相关 hook
  const {
    isDragActive,
    getInputProps,
    getRootProps,
    open,
    handleLoadFromUrl,
    handleLoadSampleImage,
    processingImageIds,
    batchRemoveBackground,
    addProcessingImages,
    removeProcessingImages,
    isLoadingApi,
  } = useBatchImageUpload(isVipUser);

  // 编辑相关 hook - 只取批量编辑需要的部分
  const { images } = useImageEditor(processingImageIds);

  // 批量历史记录 hook
  const {
    recordBatchOperation,
    recordInitialState,
    handleUndo,
    handleRedo,
    canUndo,
    canRedo,
  } = useBatchHistory();

  const handleFeatureChange = (featureId: string) => {
    setActiveFeature(featureId);
  };

  // 处理背景图片上传
  const handleFileUpload = (file: File) => {
    const objectURL = URL.createObjectURL(file);
    const newImage: UploadedImage = {
      id: `uploaded-${Date.now()}`,
      url: objectURL,
      name: file.name,
      timestamp: Date.now(),
      file,
    };
    setUploadedImages(prev => [newImage, ...prev]);
  };

  const handleApplySettings = async (settings: BackgroundSettings) => {
    console.log('批量应用背景设置:', settings);

    // 获取所有图片ID用于历史记录
    const imageIds = images.map(img => img.id);

    // 记录初始状态（仅在第一次操作时）
    recordInitialState(imageIds);

    // 批量应用背景设置到所有图片（包括锁定的图片）
    // 使用updateImage方法确保触发自动保存机制
    const imageStore = useImageStore.getState();

    images.forEach(image => {
      const existingImage = imageStore.images.get(image.id);
      if (existingImage) {
        // 使用updateImage方法更新背景设置，这会触发自动保存
        imageStore.updateImage(image.id, {
          backgroundColor: settings.color || 'transparent',
          backgroundImageUrl: settings.backgroundImageUrl,
          backgroundImageId: settings.backgroundImageId,
        });
      }
    });

    // 异步更新所有图片的预览URL
    await useImageStore
      .getState()
      .batchUpdatePreviewUrls(imageIds)
      .catch(error => {
        console.error('批量更新预览URL失败:', error);
      });

    // 记录操作后的状态到历史记录
    const description =
      settings.type === 'color'
        ? `应用背景颜色: ${settings.color}`
        : '应用背景图片';
    recordBatchOperation('background', description, imageIds);

    if (settings.type === 'color') {
      console.log(
        `已批量应用背景颜色: ${settings.color} 到 ${images.length} 张图片`
      );
    } else {
      console.log(
        `已批量应用背景图片: ${settings.backgroundImageUrl} 到 ${images.length} 张图片`
      );
    }
  };

  const handleApplyRename = async (settings: RenameSettings) => {
    console.log('Applying rename settings:', settings);
    console.log('To images:', images);

    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      console.warn('没有可处理的图片（所有图片都已锁定）');
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
      console.log(
        `跳过 ${lockedCount} 张锁定的图片，处理 ${imageIds.length} 张图片`
      );
    }

    // 记录初始状态（仅在第一次操作时）
    recordInitialState(imageIds);

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      await useImageStore
        .getState()
        .batchRenameImages(
          imageIds,
          settings.prefix,
          settings.startNumber,
          settings.numberStep,
          (current: number, total: number, currentImageId?: string) => {
            // 进度回调
            console.log(
              `重命名进度: ${current}/${total}, 当前图片: ${currentImageId}`
            );
          }
        );

      // 记录操作后的状态到历史记录
      const description = `批量重命名: ${settings.prefix}(${settings.startNumber})`;
      recordBatchOperation('rename', description, imageIds);

      console.log(
        `已批量重命名 ${imageIds.length} 张图片，前缀: "${settings.prefix}", 起始序号: ${settings.startNumber}, 间隔: ${settings.numberStep}`
      );
    } catch (error) {
      console.error('批量重命名失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const handleApplyConvert = async (settings: ConvertSettings) => {
    console.log('Applying convert settings:', settings);
    console.log('To images:', images);

    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      console.warn('没有可处理的图片（所有图片都已锁定）');
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
      console.log(
        `跳过 ${lockedCount} 张锁定的图片，处理 ${imageIds.length} 张图片`
      );
    }

    // 记录初始状态（仅在第一次操作时）
    recordInitialState(imageIds);

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      await useImageStore
        .getState()
        .batchConvertImages(
          imageIds,
          settings.format,
          (current: number, total: number, currentImageId?: string) => {
            // 进度回调
            console.log(
              `格式转换进度: ${current}/${total}, 当前图片: ${currentImageId}`
            );
          }
        );

      // 记录操作后的状态到历史记录
      const description = `格式转换: ${settings.format}`;
      recordBatchOperation('convert', description, imageIds);

      console.log(
        `已批量转换 ${imageIds.length} 张图片格式为: ${settings.format}`
      );
    } catch (error) {
      console.error('批量格式转换失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const handleApplyCompress = async (settings: CompressSettings) => {
    console.log('Applying compress settings:', settings);
    console.log('To images:', images);

    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      console.warn('没有可处理的图片（所有图片都已锁定）');
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
      console.log(
        `跳过 ${lockedCount} 张锁定的图片，处理 ${imageIds.length} 张图片`
      );
    }

    // 记录初始状态（仅在第一次操作时）
    recordInitialState(imageIds);

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      await useImageStore
        .getState()
        .batchCompressImages(
          imageIds,
          settings.level,
          settings.customSize,
          settings.customUnit,
          (current: number, total: number, currentImageId?: string) => {
            // 进度回调
            console.log(
              `压缩进度: ${current}/${total}, 当前图片: ${currentImageId}`
            );
          }
        );

      // 记录操作后的状态到历史记录
      const description = settings.level
        ? `压缩: ${settings.level}`
        : `压缩: ${settings.customSize}${settings.customUnit}`;
      recordBatchOperation('compress', description, imageIds);

      console.log(`已批量压缩 ${imageIds.length} 张图片`);
    } catch (error) {
      console.error('批量压缩失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const handleApplyResize = async (settings: ResizeSettings) => {
    console.log('Applying resize settings:', settings);
    console.log('To images:', images);

    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      console.warn('没有可处理的图片（所有图片都已锁定）');
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
      console.log(
        `跳过 ${lockedCount} 张锁定的图片，处理 ${imageIds.length} 张图片`
      );
    }

    // 记录初始状态（仅在第一次操作时）
    recordInitialState(imageIds);

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      if (settings.mode === 'fixed' && settings.width && settings.height) {
        // 固定尺寸模式
        await useImageStore.getState().batchResizeImages(
          imageIds,
          settings.width,
          settings.height,
          'fit', // 默认使用 fit 模式
          (current: number, total: number, currentImageId?: string) => {
            // 进度回调
            console.log(
              `尺寸调整进度: ${current}/${total}, 当前图片: ${currentImageId}`
            );
          }
        );

        console.log(
          `已批量应用固定尺寸调整: ${settings.width}x${settings.height} 到 ${images.length} 张图片`
        );
      } else if (settings.mode === 'ratio' && settings.aspectRatio) {
        // 宽高比模式 - 需要为每张图片计算具体尺寸
        await useImageStore.getState().batchResizeImagesByRatio(
          imageIds,
          settings.aspectRatio,
          'fit', // 默认使用 fit 模式
          (current: number, total: number, currentImageId?: string) => {
            // 进度回调
            console.log(
              `宽高比调整进度: ${current}/${total}, 当前图片: ${currentImageId}`
            );
          }
        );

        console.log(
          `已批量应用宽高比调整: ${settings.aspectRatio} 到 ${images.length} 张图片`
        );
      } else if (settings.mode === 'scale' && settings.scale) {
        // 按比例缩放模式
        await useImageStore
          .getState()
          .batchResizeImagesByScale(
            imageIds,
            settings.scale,
            (current: number, total: number, currentImageId?: string) => {
              // 进度回调
              console.log(
                `比例缩放进度: ${current}/${total}, 当前图片: ${currentImageId}`
              );
            }
          );

        console.log(
          `已批量应用比例缩放: ${(settings.scale * 100).toFixed(0)}% 到 ${images.length} 张图片`
        );
      } else if (
        settings.mode === 'custom' &&
        settings.customMode === 'scale' &&
        settings.scale
      ) {
        // 自定义缩放模式
        await useImageStore
          .getState()
          .batchResizeImagesByScale(
            imageIds,
            settings.scale,
            (current: number, total: number, currentImageId?: string) => {
              // 进度回调
              console.log(
                `自定义缩放进度: ${current}/${total}, 当前图片: ${currentImageId}`
              );
            }
          );

        console.log(
          `已批量应用自定义缩放: ${(settings.scale * 100).toFixed(0)}% 到 ${images.length} 张图片`
        );
      } else if (
        settings.mode === 'custom' &&
        settings.customMode === 'dimensions' &&
        settings.width &&
        settings.height
      ) {
        // 自定义尺寸模式
        await useImageStore.getState().batchResizeImages(
          imageIds,
          settings.width,
          settings.height,
          'fit', // 默认使用 fit 模式
          (current: number, total: number, currentImageId?: string) => {
            // 进度回调
            console.log(
              `自定义尺寸调整进度: ${current}/${total}, 当前图片: ${currentImageId}`
            );
          }
        );

        console.log(
          `已批量应用自定义尺寸: ${settings.width}x${settings.height} (${settings.unit || 'px'}) 到 ${images.length} 张图片`
        );
      }

      // 记录操作后的状态到历史记录
      let description = '';
      if (settings.mode === 'fixed') {
        description = `尺寸调整: ${settings.width}x${settings.height}`;
      } else if (settings.mode === 'ratio') {
        description = `宽高比调整: ${settings.aspectRatio}`;
      } else if (settings.mode === 'scale') {
        description = `缩放: ${(settings.scale || 1) * 100}%`;
      } else {
        description = '自定义尺寸调整';
      }
      recordBatchOperation('resize', description, imageIds);
    } catch (error) {
      console.error('批量尺寸调整失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const renderSettingsPanel = () => {
    switch (activeFeature) {
      case 'background':
        return (
          <BackgroundSettings
            onApply={handleApplySettings}
            onBatchRemoveBackground={batchRemoveBackground}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              processedUrl: img.processedUrl,
            }))}
            isProcessing={isLoadingApi}
            uploadedImages={uploadedImages}
            onFileUpload={handleFileUpload}
            uploadedBackgroundImages={uploadedBackgroundImages}
            onAddBackgroundImage={addBackgroundImage}
            onDeleteBackgroundImage={deleteBackgroundImage}
          />
        );
      case 'resize':
        return (
          <ResizeSettings
            onApply={handleApplyResize}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              processedUrl: img.processedUrl,
              targetWidth: img.targetWidth,
              targetHeight: img.targetHeight,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      case 'rename':
        return (
          <RenameSettings
            onApply={handleApplyRename}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      case 'convert':
        return (
          <ConvertSettings
            onApply={handleApplyConvert}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              originalFormat: img.originalFormat,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      case 'compress':
        return (
          <CompressSettings
            onApply={handleApplyCompress}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              originalSize: img.originalSize,
              compressedSize: img.compressedSize,
              size: img.size,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      default:
        return null;
    }
  };

  console.log(images);

  // 如果存储初始化失败，显示错误信息
  if (initializationStatus === 'error') {
    return (
      <div className={`flex flex-col h-screen bg-white ${className || ''}`}>
        <Header variant='batch-editor' />
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <h2 className='text-xl font-semibold text-red-600 mb-2'>
              存储初始化失败
            </h2>
            <p className='text-gray-600 mb-4'>{initializationError}</p>
            <button
              onClick={() => window.location.reload()}
              className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 如果存储正在初始化，显示加载状态
  if (initializationStatus === 'initializing') {
    return (
      <div className={`flex flex-col h-screen bg-white ${className || ''}`}>
        <Header variant='batch-editor' />
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4'></div>
            <p className='text-gray-600'>正在初始化存储...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-screen bg-white ${className || ''}`}>
      {/* 始终渲染 dropzone 的 input 元素，确保 open() 函数可用 */}
      <input {...getInputProps()} />
      {/* 顶部导航栏 */}
      <Header variant='batch-editor' />

      {/* 主要内容区域 */}
      <div className='flex flex-1'>
        {/* 左侧导航 */}
        <SideNavigation
          activeItem={activeFeature}
          onItemChange={handleFeatureChange}
        />

        {/* 中间设置面板 */}
        {renderSettingsPanel()}

        {/* 右侧图片区域 */}
        <ImageUploadGrid
          getRootProps={getRootProps}
          open={open}
          isDragActive={isDragActive}
          handleLoadFromUrl={handleLoadFromUrl}
          handleLoadSampleImage={handleLoadSampleImage}
          imagesCount={images.length}
          images={images}
          processingImageIds={processingImageIds}
          onUndo={handleUndo}
          onRedo={handleRedo}
          canUndo={canUndo}
          canRedo={canRedo}
        />
      </div>
    </div>
  );
}
