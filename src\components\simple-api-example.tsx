'use client';

import { getUserInfo, login } from '@/lib/api';
import { useState } from 'react';
import { Button } from './ui/Button';

export function SimpleApiExample() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  // 模拟登录
  const handleLogin = async () => {
    setLoading(true);
    setResult('');

    try {
      const response = await login({
        username: '<EMAIL>',
        password: 'password123',
      });

      setResult(`登录成功: ${response.userInfo.username}`);
    } catch (error) {
      // 直接显示后端返回的错误消息
      if (error instanceof Error) {
        setResult(`登录失败: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // 模拟获取用户信息（需要认证）
  const handleGetUserInfo = async () => {
    setLoading(true);
    setResult('');

    try {
      const userInfo = await getUserInfo();
      setResult(`用户信息: ${userInfo.username} (${userInfo.email})`);
    } catch (error) {
      // 如果是 token 失效，会自动跳转到登录页
      // 这里只需要显示错误消息即可
      if (error instanceof Error) {
        setResult(`获取用户信息失败: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='p-6 max-w-md mx-auto bg-white rounded-lg shadow-md'>
      <h2 className='text-xl font-bold mb-4'>简化的 API 调用示例</h2>

      <div className='space-y-4'>
        <Button onClick={handleLogin} disabled={loading} className='w-full'>
          {loading ? '处理中...' : '模拟登录'}
        </Button>

        <Button
          onClick={handleGetUserInfo}
          disabled={loading}
          variant='outline'
          className='w-full'
        >
          {loading ? '处理中...' : '获取用户信息'}
        </Button>

        {result && (
          <div className='p-3 mt-4 text-sm bg-gray-100 rounded-md'>
            <strong>结果:</strong> {result}
          </div>
        )}

        <div className='mt-6 text-xs text-gray-600'>
          <h3 className='font-semibold mb-2'>错误处理说明:</h3>
          <ul className='space-y-1'>
            <li>• 只关注后端 code 是否为 1</li>
            <li>• 所有错误直接显示后端返回的 msg</li>
            <li>• Token 失效 (200015) 自动跳转登录页</li>
            <li>• 前端不参与复杂的错误码处理</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
