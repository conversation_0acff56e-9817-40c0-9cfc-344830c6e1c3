'use client';

import { But<PERSON> } from '@/components/ui/Button';
import { useImageStore } from '@/lib/store/imageStore';
import { Download, Plus, Upload } from 'lucide-react';
import Image from 'next/image';

/**
 * 移动端专用的背景移除组件
 * 针对移动设备优化的用户界面和交互体验
 */
export function MobileBackgroundRemover() {
  const imagesMap = useImageStore(s => s.images);
  const selectedImageIds = useImageStore(s => s.selectedImageIds);

  const currentImageId = selectedImageIds.values().next().value || null;
  const currentImage = currentImageId ? imagesMap.get(currentImageId) : null;

  // 示例图片 - 移动端显示更少
  const sampleImages = [
    {
      id: 'sample_dog',
      name: '人像',
      url: '/apps/images/mock/01_color.png',
      processedUrl: '/apps/images/mock/01_alpha.png',
    },
    {
      id: 'sample_cat',
      name: '宠物',
      url: '/apps/images/mock/02_color.png',
      processedUrl: '/apps/images/mock/02_alpha.png',
    },
  ];

  // 下载处理后的图片
  const handleDownload = async () => {
    if (!currentImage?.processedUrl) return;
  };

  return (
    <div className='h-full flex flex-col bg-gray-50 overflow-hidden'>
      {!currentImage ? (
        // 移动端初始上传界面
        <div className='flex-1 flex flex-col items-center justify-center px-4 py-8'>
          <h1 className='text-xl font-bold text-center text-gray-900 mb-8'>
            上传图片移除背景
          </h1>

          {/* 移动端上传区域 */}
          <div className='w-full max-w-md bg-white rounded-2xl shadow-lg p-6 mb-8'>
            <div className='border-2 border-dashed border-gray-300 rounded-xl p-8 text-center'>
              <div className='mb-4'>
                <div className='w-16 h-16 mx-auto bg-yellow-400 rounded-full flex items-center justify-center'>
                  <Plus className='w-8 h-8 text-gray-900' />
                </div>
              </div>

              <p className='text-gray-600 mb-4 text-sm'>拖拽图片到这里</p>

              <Button
                size='lg'
                className='bg-yellow-400 text-gray-900 hover:bg-yellow-500 w-full mb-4'
                onClick={e => {
                  e.stopPropagation();
                  open();
                }}
              >
                <Upload className='w-5 h-5 mr-2' />
                选择图片
              </Button>

              <p className='text-xs text-gray-500'>支持 JPG, PNG, WebP 格式</p>
            </div>
          </div>

          {/* 移动端示例图片 */}
          <div className='w-full max-w-md'>
            <h3 className='text-sm text-gray-600 text-center mb-4'>
              或试试这些示例：
            </h3>
            <div className='flex justify-center gap-4'>
              {sampleImages.map(sample => (
                <div key={sample.id} className='cursor-pointer'>
                  <div className='w-20 h-20 rounded-lg overflow-hidden bg-gray-300 shadow-md'>
                    <Image
                      src={sample.url}
                      alt={sample.name}
                      className='w-full h-full object-cover'
                      width={80}
                      height={80}
                    />
                  </div>
                  <p className='text-xs text-center mt-2 text-gray-600'>
                    {sample.name}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        // 移动端图片编辑界面
        <div className='flex-1 flex flex-col'>
          {/* 图片显示区域 */}
          <div className='flex-1 flex items-center justify-center p-4'>
            <div className='relative max-w-full max-h-full'>
              <Image
                src={currentImage.processedUrl || ''}
                alt='Processed'
                className='max-w-full max-h-full object-contain rounded-lg shadow-lg'
                width={800}
                height={600}
                style={{
                  width: 'auto',
                  height: 'auto',
                  maxWidth: '100%',
                  maxHeight: '100%',
                }}
              />
            </div>
          </div>

          {/* 移动端操作按钮 */}
          <div className='p-4 bg-white border-t border-gray-200'>
            <div className='flex gap-3'>
              <Button variant='outline' className='flex-1'>
                重新上传
              </Button>

              <Button
                onClick={handleDownload}
                className='flex-1 bg-yellow-400 text-gray-900 hover:bg-yellow-500'
                disabled={!currentImage.processedUrl}
              >
                <Download className='w-4 h-4 mr-2' />
                下载
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
