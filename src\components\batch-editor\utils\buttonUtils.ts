/**
 * 批量操作按钮的通用工具函数
 */

/**
 * 判断批量操作的 Apply 按钮是否应该被禁用
 * @param isProcessing 是否正在处理
 * @param images 图片列表
 * @returns 是否应该禁用按钮
 */
export function shouldDisableBatchApplyButton(
  isProcessing: boolean,
  images: Array<{ status: string }>
): boolean {
  // 正在处理时禁用
  if (isProcessing) {
    return true;
  }

  // 计算可处理的图片数量（排除锁定的图片）
  const unlockedImagesCount = images.filter(
    img => img.status !== 'locked'
  ).length;

  // 没有可处理的图片时禁用
  return unlockedImagesCount === 0;
}

/**
 * 获取可处理的图片数量（排除锁定的图片）
 * @param images 图片列表
 * @returns 可处理的图片数量
 */
export function getUnlockedImagesCount(
  images: Array<{ status: string }>
): number {
  return images.filter(img => img.status !== 'locked').length;
}
