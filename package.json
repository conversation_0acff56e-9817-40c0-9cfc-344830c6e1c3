{"name": "ai-bg-remover", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}, "scripts": {"dev": "next dev", "dev:test": "cross-env NODE_ENV=test next dev", "build": "next build", "build:test": "cross-env NODE_ENV=test next build", "build:prod": "cross-env NODE_ENV=production next build", "start": "next start", "start:test": "cross-env NODE_ENV=test next start -p 8004", "start:prod": "cross-env NODE_ENV=production next start -p 8004", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/jszip": "^3.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "immer": "^10.1.1", "jszip": "^3.10.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "tailwind-merge": "^3.3.0", "zundo": "^2.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.0", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,css,md}": ["prettier --write"]}}