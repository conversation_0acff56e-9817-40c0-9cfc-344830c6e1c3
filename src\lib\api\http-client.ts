interface ApiResponse<T = unknown> {
  code: number;
  data: T;
  msg: string;
}

interface HttpClientConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

class HttpClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(config: HttpClientConfig = {}) {
    this.baseURL = config.baseURL || process.env.NEXT_PUBLIC_API_BASE_URL!;
    this.timeout = config.timeout || 10000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...config.headers,
    };
  }

  // 获取存储的 token
  private getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('token');
  }

  // 设置 token
  public setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', token);
    }
  }

  // 清除 token
  public clearToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
    }
  }

  // 请求拦截器 - 添加 token 到请求头
  private addAuthHeader(
    headers: Record<string, string>
  ): Record<string, string> {
    const token = this.getToken();
    if (token) {
      return {
        ...headers,
        Authorization: `Bearer ${token}`,
      };
    }
    return headers;
  }

  // 处理 token 失效
  private handleTokenExpired(): void {
    this.clearToken();

    if (typeof window !== 'undefined') {
      // 清除 cookie 中的 token
      document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

      // 重定向到登录页，保留当前路径
      const currentPath = window.location.pathname;
      const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
      window.location.href = loginUrl;
    }
  }

  // 响应拦截器 - 简化的错误处理
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');

    // 如果是图片等二进制数据，直接返回 Response
    if (contentType && contentType.includes('image/')) {
      return response as unknown as T;
    }

    // 处理 JSON 响应
    let data: ApiResponse<T>;
    try {
      data = await response.json();
    } catch {
      throw new Error('服务器响应格式错误');
    }

    // 只关注 code 是否为 1，成功则返回数据
    if (data.code === 1) {
      return data.data;
    }

    // 特殊处理 token 失效
    if (data.code === 200015) {
      this.handleTokenExpired();
      throw new Error(data.msg || 'Token已失效，请重新登录');
    }

    // 其他所有非 1 的情况，直接抛出后端返回的 msg
    throw new Error(data.msg || '请求失败');
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = this.addAuthHeader({
      ...this.defaultHeaders,
      ...(options.headers as Record<string, string>),
    });

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      return await this.handleResponse<T>(response);
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时');
        }
        throw error;
      }
      throw new Error('网络请求失败');
    }
  }

  // GET 请求
  public async get<T>(
    endpoint: string,
    params?: Record<string, string | number | boolean>
  ): Promise<T> {
    const searchParams = params
      ? '?' +
        new URLSearchParams(
          Object.entries(params).map(([k, v]) => [k, String(v)])
        ).toString()
      : '';

    return this.request<T>(`${endpoint}${searchParams}`, {
      method: 'GET',
    });
  }

  // POST 请求
  public async post<T>(endpoint: string, data?: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT 请求
  public async put<T>(endpoint: string, data?: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE 请求
  public async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // 文件上传
  public async upload<T>(endpoint: string, formData: FormData): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // 只添加认证头，不设置 Content-Type，让浏览器自动设置 multipart/form-data
    const headers = this.addAuthHeader({});
    delete headers['Content-Type']; // 确保删除 Content-Type

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      return await this.handleResponse<T>(response);
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时');
        }
        throw error;
      }
      throw new Error('网络请求失败');
    }
  }
}

// 创建默认实例
export const httpClient = new HttpClient();

// 导出类型
export { HttpClient };
export type { ApiResponse };
