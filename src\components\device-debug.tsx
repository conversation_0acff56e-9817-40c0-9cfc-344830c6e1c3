'use client';

import { useDevice } from '@/lib/context/device-context';
import { useState, useEffect } from 'react';

/**
 * 设备调试组件 - 开发环境使用
 * 显示当前检测到的设备类型，用于调试设备检测功能
 */
export function DeviceDebug() {
  const { deviceType, isMobile, isDesktop } = useDevice();
  const [screenWidth, setScreenWidth] = useState<number | string>('N/A');

  useEffect(() => {
    // 确保只在客户端执行
    if (typeof window !== 'undefined') {
      setScreenWidth(window.innerWidth);
    }
  }, []);

  // 仅在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className='fixed top-4 right-4 z-50 bg-black bg-opacity-80 text-white px-3 py-2 rounded-lg text-xs font-mono'>
      <div>设备类型: {deviceType}</div>
      <div>移动端: {isMobile ? '是' : '否'}</div>
      <div>桌面端: {isDesktop ? '是' : '否'}</div>
      <div>屏幕宽度: {screenWidth}px</div>
    </div>
  );
}
