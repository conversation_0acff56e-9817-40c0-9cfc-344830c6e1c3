/**
 * 统一的图片处理管道
 * 将多种批量操作（背景处理、尺寸调整、压缩、格式转换）按顺序应用到同一个canvas上
 * 实现真正的操作组合，而不是简单的URL替换
 */

import type { SupportedFormat } from '@/lib/utils/imageConvert';
import {
  getFormatMimeType,
  formatSupportsTransparency,
} from '@/lib/utils/imageConvert';
import { calculateResizeInfo, type ResizeMode } from '@/lib/utils/imageResize';
import { getQualityByLevel } from '@/lib/utils/imageCompress';

/**
 * 图片处理操作配置
 */
export interface ImageProcessingConfig {
  // 背景处理
  backgroundColor?: string;
  backgroundImageUrl?: string;

  // 尺寸调整
  targetWidth?: number;
  targetHeight?: number;
  resizeMode?: ResizeMode;

  // 格式转换
  outputFormat?: SupportedFormat;

  // 压缩设置
  compressionLevel?: 'original' | 'light' | 'medium' | 'deep';
  customCompressionSize?: number;
  customCompressionUnit?: 'KB' | 'MB';

  // 其他设置
  quality?: number; // 输出质量 (0-1)
}

/**
 * 处理结果
 */
export interface ProcessingResult {
  dataUrl: string;
  width: number;
  height: number;
  size: number; // 文件大小（字节）
  format: SupportedFormat;
}

/**
 * 加载图片对象
 */
function loadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('图片加载失败'));

    img.src = src;
  });
}

/**
 * 加载背景图片对象
 */
async function loadBackgroundImage(
  backgroundImageUrl: string
): Promise<HTMLImageElement> {
  return loadImage(backgroundImageUrl);
}

/**
 * 在canvas上绘制背景
 */
function drawBackground(
  ctx: CanvasRenderingContext2D,
  canvas: HTMLCanvasElement,
  backgroundColor?: string,
  backgroundImage?: HTMLImageElement
) {
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  if (backgroundImage) {
    // 绘制背景图片 - 使用cover模式填充整个画布
    const imgAspect =
      backgroundImage.naturalWidth / backgroundImage.naturalHeight;
    const canvasAspect = canvas.width / canvas.height;

    let sx, sy, sWidth, sHeight;

    if (imgAspect > canvasAspect) {
      // 图片更宽，以高度为准
      sHeight = backgroundImage.naturalHeight;
      sWidth = sHeight * canvasAspect;
      sx = (backgroundImage.naturalWidth - sWidth) / 2;
      sy = 0;
    } else {
      // 图片更高，以宽度为准
      sWidth = backgroundImage.naturalWidth;
      sHeight = sWidth / canvasAspect;
      sx = 0;
      sy = (backgroundImage.naturalHeight - sHeight) / 2;
    }

    ctx.drawImage(
      backgroundImage,
      sx,
      sy,
      sWidth,
      sHeight,
      0,
      0,
      canvas.width,
      canvas.height
    );
  } else if (backgroundColor && backgroundColor !== 'transparent') {
    // 绘制纯色背景
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }
}

/**
 * 计算压缩质量
 */
function calculateCompressionQuality(config: ImageProcessingConfig): number {
  if (config.quality !== undefined) {
    return config.quality;
  }

  if (config.compressionLevel) {
    return getQualityByLevel(config.compressionLevel);
  }

  // 默认质量
  return 0.9;
}

/**
 * 计算文件大小（估算）
 */
function estimateFileSize(dataUrl: string): number {
  // 移除data URL前缀
  const base64Data = dataUrl.split(',')[1];
  if (!base64Data) return 0;

  // Base64编码后的大小约为原始大小的4/3
  return Math.round((base64Data.length * 3) / 4);
}

/**
 * 按目标文件大小压缩图片
 */
async function compressToTargetSize(
  canvas: HTMLCanvasElement,
  targetSizeBytes: number,
  format: SupportedFormat,
  maxIterations: number = 10
): Promise<string> {
  const mimeType = getFormatMimeType(format);
  let minQuality = 0.1;
  let maxQuality = 1.0;
  let bestDataUrl = canvas.toDataURL(mimeType, 0.9);

  for (let i = 0; i < maxIterations; i++) {
    const currentQuality = (minQuality + maxQuality) / 2;
    const dataUrl = canvas.toDataURL(mimeType, currentQuality);
    const currentSize = estimateFileSize(dataUrl);

    if (Math.abs(currentSize - targetSizeBytes) < targetSizeBytes * 0.05) {
      // 误差在5%以内，认为达到目标
      return dataUrl;
    }

    if (currentSize > targetSizeBytes) {
      maxQuality = currentQuality;
    } else {
      minQuality = currentQuality;
      bestDataUrl = dataUrl;
    }
  }

  return bestDataUrl;
}

/**
 * 生成预览URL - 轻量版处理管道，用于实时预览
 * 只应用背景和尺寸调整，不进行压缩和格式转换
 */
export async function generatePreviewUrl(
  imageUrl: string,
  processedImageUrl: string | null,
  config: Pick<
    ImageProcessingConfig,
    | 'backgroundColor'
    | 'backgroundImageUrl'
    | 'targetWidth'
    | 'targetHeight'
    | 'resizeMode'
  >
): Promise<string> {
  try {
    // 如果没有需要处理的操作，直接返回原URL
    const needsProcessing =
      (config.backgroundColor && config.backgroundColor !== 'transparent') ||
      config.backgroundImageUrl ||
      (config.targetWidth && config.targetHeight);

    if (!needsProcessing) {
      return processedImageUrl || imageUrl;
    }

    // 使用完整处理管道，但设置为PNG格式且不压缩
    const fullConfig: ImageProcessingConfig = {
      ...config,
      outputFormat: 'png',
      quality: 1.0, // 不压缩，保持最高质量
    };

    const result = await processImagePipeline(
      imageUrl,
      processedImageUrl,
      fullConfig
    );
    return result.dataUrl;
  } catch (error) {
    console.warn('预览URL生成失败，使用原始URL:', error);
    return processedImageUrl || imageUrl;
  }
}

/**
 * 统一的图片处理管道
 * 按照正确的顺序应用所有操作：
 * 1. 加载原始图片
 * 2. 应用背景处理（如果需要）
 * 3. 应用尺寸调整（如果需要）
 * 4. 应用格式转换和压缩
 */
export async function processImagePipeline(
  imageUrl: string,
  processedImageUrl: string | null, // 去背后的图片URL
  config: ImageProcessingConfig
): Promise<ProcessingResult> {
  try {
    // 1. 加载图片
    const sourceImage = await loadImage(processedImageUrl || imageUrl);
    let backgroundImage: HTMLImageElement | undefined;

    if (config.backgroundImageUrl) {
      try {
        backgroundImage = await loadBackgroundImage(config.backgroundImageUrl);
      } catch (error) {
        console.warn('背景图片加载失败，将使用透明背景:', error);
      }
    }

    // 2. 确定最终尺寸
    let finalWidth = sourceImage.naturalWidth;
    let finalHeight = sourceImage.naturalHeight;

    if (config.targetWidth && config.targetHeight) {
      finalWidth = config.targetWidth;
      finalHeight = config.targetHeight;
    }

    // 3. 创建canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法创建Canvas 2D上下文');
    }

    canvas.width = finalWidth;
    canvas.height = finalHeight;

    // 设置高质量渲染
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 4. 确定输出格式
    const outputFormat = config.outputFormat || 'png';
    const mimeType = getFormatMimeType(outputFormat);

    // 5. 绘制背景（如果输出格式不支持透明度，需要白色背景）
    if (
      !formatSupportsTransparency(outputFormat) &&
      !config.backgroundColor &&
      !backgroundImage
    ) {
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    } else if (config.backgroundColor || backgroundImage) {
      drawBackground(ctx, canvas, config.backgroundColor, backgroundImage);
    }

    // 6. 绘制主图片
    if (config.targetWidth && config.targetHeight && config.resizeMode) {
      // 需要尺寸调整
      const resizeInfo = calculateResizeInfo(
        sourceImage.naturalWidth,
        sourceImage.naturalHeight,
        config.targetWidth,
        config.targetHeight,
        config.resizeMode
      );

      if (
        resizeInfo.sourceX !== undefined &&
        resizeInfo.sourceY !== undefined &&
        resizeInfo.sourceWidth !== undefined &&
        resizeInfo.sourceHeight !== undefined
      ) {
        // fill模式，需要裁剪源图片
        ctx.drawImage(
          sourceImage,
          resizeInfo.sourceX,
          resizeInfo.sourceY,
          resizeInfo.sourceWidth,
          resizeInfo.sourceHeight,
          resizeInfo.drawX,
          resizeInfo.drawY,
          resizeInfo.drawWidth,
          resizeInfo.drawHeight
        );
      } else {
        // fit或stretch模式
        ctx.drawImage(
          sourceImage,
          resizeInfo.drawX,
          resizeInfo.drawY,
          resizeInfo.drawWidth,
          resizeInfo.drawHeight
        );
      }
    } else {
      // 不需要尺寸调整，直接绘制
      ctx.drawImage(sourceImage, 0, 0, finalWidth, finalHeight);
    }

    // 7. 生成最终图片
    let finalDataUrl: string;

    if (config.customCompressionSize && config.customCompressionUnit) {
      // 按目标文件大小压缩
      const targetSizeBytes =
        config.customCompressionUnit === 'MB'
          ? config.customCompressionSize * 1024 * 1024
          : config.customCompressionSize * 1024;

      finalDataUrl = await compressToTargetSize(
        canvas,
        targetSizeBytes,
        outputFormat
      );
    } else {
      // 按质量压缩
      const quality = calculateCompressionQuality(config);
      finalDataUrl = canvas.toDataURL(mimeType, quality);
    }

    // 8. 返回结果
    return {
      dataUrl: finalDataUrl,
      width: finalWidth,
      height: finalHeight,
      size: estimateFileSize(finalDataUrl),
      format: outputFormat,
    };
  } catch (error) {
    throw new Error(
      `图片处理管道失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}
