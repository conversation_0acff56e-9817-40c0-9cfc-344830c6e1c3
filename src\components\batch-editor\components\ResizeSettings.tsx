'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Lock, Unlock } from 'lucide-react';
import Image from 'next/image';
import { Slider } from '@/components/ui/Slider';
import { useTips } from '@/components/ui/Tips';
import { shouldDisableBatchApplyButton } from '../utils/buttonUtils';

interface ResizeSettings {
  width?: number;
  height?: number;
  aspectRatio?: number; // 宽高比 (width/height)
  scale?: number; // 缩放比例 (0.2-2.0)
  mode: 'fixed' | 'ratio' | 'scale' | 'custom'; // 固定尺寸、宽高比、缩放或自定义模式
  customMode?: 'scale' | 'dimensions'; // 自定义模式的子类型
  unit?: 'px' | 'in' | 'mm'; // 单位
  lockAspectRatio?: boolean; // 是否锁定宽高比
}

interface ResizeSettingsProps {
  onApply?: (settings: ResizeSettings) => void;
  images?: Array<{
    id: string;
    status: string;
    processedUrl?: string | null;
    targetWidth?: number;
    targetHeight?: number;
    width?: number;
    height?: number;
  }>;
  isProcessing?: boolean;
}

// 预设类型定义
interface FixedPreset {
  id: string;
  name: string;
  width: number;
  height: number;
  icon: string;
  mode: 'fixed';
}

interface RatioPreset {
  id: string;
  name: string;
  aspectRatio: number;
  icon: string;
  mode: 'ratio';
}

// 平台尺寸预设数据
const RESIZE_PRESETS = {
  marketplace: {
    title: 'For Marketplace Platforms',
    presets: [
      {
        id: 'tiktok',
        name: 'TikTok',
        width: 1600,
        height: 1600,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'amazon',
        name: 'Amazon',
        width: 2000,
        height: 2000,
        icon: '/apps/images/platform/amazon.png',
        mode: 'fixed',
      },
      {
        id: 'ebay',
        name: 'eBay',
        width: 1600,
        height: 1600,
        icon: '/apps/images/platform/ebay.png',
        mode: 'fixed',
      },
      {
        id: 'postmark',
        name: 'Postmark',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/postmark.png',
        mode: 'fixed',
      },
      {
        id: 'depop',
        name: 'Depop',
        width: 1280,
        height: 1280,
        icon: '/apps/images/platform/depop.png',
        mode: 'fixed',
      },
      {
        id: 'mercari',
        name: 'Mercari',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/mercari.png',
        mode: 'fixed',
      },
      {
        id: 'mercado-libre',
        name: 'Mercado Libre',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/mercadoLibre.png',
        mode: 'fixed',
      },
      {
        id: 'shopee',
        name: 'Shopee',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/shopee.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-square',
        name: 'Shopify square',
        width: 2048,
        height: 2048,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-landscape',
        name: 'Shopify landscape',
        width: 2000,
        height: 1800,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-portrait',
        name: 'Shopify portrait',
        width: 1600,
        height: 2000,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'lazada',
        name: 'Lazada',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/lazada.png',
        mode: 'fixed',
      },
      {
        id: 'etsy',
        name: 'Etsy',
        width: 2700,
        height: 2025,
        icon: '/apps/images/platform/etsy.png',
        mode: 'fixed',
      },
      {
        id: 'vinted',
        name: 'Vinted',
        width: 800,
        height: 600,
        icon: '/apps/images/platform/vinted.png',
        mode: 'fixed',
      },
    ],
  },
  social: {
    title: 'For Social Media Platforms',
    presets: [
      {
        id: 'ins-story',
        name: 'INS',
        width: 1080,
        height: 1920,
        icon: '/apps/images/platform/ins.png',
        mode: 'fixed',
      },
      {
        id: 'ins-post',
        name: 'INS',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/ins.png',
        mode: 'fixed',
      },
      {
        id: 'fb-cover',
        name: 'FB cover',
        width: 820,
        height: 312,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'fb-post',
        name: 'FB post',
        width: 1200,
        height: 628,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'fb-marketplace',
        name: 'FB Marketplace',
        width: 1200,
        height: 628,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'tiktok-post',
        name: 'TIKTOK post',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'tiktok-cover',
        name: 'TIKTOK cover',
        width: 1080,
        height: 1440,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'youtube-cover',
        name: 'YouTube cover',
        width: 1280,
        height: 1920,
        icon: '/apps/images/platform/youtube.png',
        mode: 'fixed',
      },
      {
        id: 'youtube-channel',
        name: 'YouTube channel',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/youtube.png',
        mode: 'fixed',
      },
      {
        id: 'twitter-cover',
        name: 'Twitter cover',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/x.png',
        mode: 'fixed',
      },
      {
        id: 'twitter-post',
        name: 'Twitter post',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/x.png',
        mode: 'fixed',
      },
    ],
  },
  ratio: {
    title: 'Ratio Size',
    presets: [
      {
        id: 'square',
        name: 'Square(1:1)',
        aspectRatio: 1, // 1:1
        icon: '⬜',
        mode: 'ratio',
      },
      {
        id: 'ratio-3-2',
        name: '3:2',
        aspectRatio: 3 / 2, // 1.5
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-4-3',
        name: '4:3',
        aspectRatio: 4 / 3, // 1.333...
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-5-3',
        name: '5:3',
        aspectRatio: 5 / 3, // 1.666...
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-5-4',
        name: '5:4',
        aspectRatio: 5 / 4, // 1.25
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-7-5',
        name: '7:5',
        aspectRatio: 7 / 5, // 1.4
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-16-9',
        name: '16:9',
        aspectRatio: 16 / 9, // 1.777...
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'passport-2x2',
        name: 'Passport & Photo(2 x 2in)',
        width: 600,
        height: 600,
        icon: '🆔',
        mode: 'fixed',
      },
      {
        id: 'passport-3.5x4.5',
        name: 'Passport & Photo(3.5 x 4.5in)',
        width: 1050,
        height: 1350,
        icon: '🆔',
        mode: 'fixed',
      },
    ],
  },
  custom: {
    title: 'Custom Size',
    presets: [], // Custom Size 不需要预设列表
  },
};

export function ResizeSettings({
  onApply,
  images = [],
  isProcessing = false,
}: ResizeSettingsProps) {
  const { showTips } = useTips();
  const [selectedCategory, setSelectedCategory] = useState<
    'marketplace' | 'social' | 'ratio' | 'custom'
  >('marketplace');
  const [selectedPreset, setSelectedPreset] = useState<string>('tiktok');

  // Custom Size 相关状态
  const [customMode, setCustomMode] = useState<'scale' | 'dimensions'>('scale');
  const [scaleValue, setScaleValue] = useState<number>(100); // 百分比 (20-200)
  const [customWidth, setCustomWidth] = useState<string>('');
  const [customHeight, setCustomHeight] = useState<string>('');
  const [lockAspectRatio, setLockAspectRatio] = useState<boolean>(true);
  const [unit, setUnit] = useState<'px' | 'in' | 'mm'>('px');

  // 单位转换函数
  const convertToPixels = (
    value: number,
    fromUnit: 'px' | 'in' | 'mm'
  ): number => {
    switch (fromUnit) {
      case 'px':
        return value;
      case 'in':
        return value * 96; // 1 inch = 96 pixels
      case 'mm':
        return value * 3.78; // 1 mm ≈ 3.78 pixels
      default:
        return value;
    }
  };

  // 验证尺寸函数
  const validateDimensions = (width: number, height: number): string => {
    const minPx = 300;
    const maxPx = 4000;

    if (width < minPx || height < minPx) {
      return `Size too small. Minimum value is 300 px (≈ 3.1 inches or 79 mm).`;
    }

    if (width > maxPx || height > maxPx) {
      return `Size too large. Maximum value is 4000 px (≈ 42 inches or 1066 mm).`;
    }

    return '';
  };

  const handleApply = () => {
    // Custom Size 分类的特殊处理
    if (selectedCategory === 'custom' && onApply) {
      if (customMode === 'scale') {
        // 按比例缩放
        onApply({
          scale: scaleValue / 100, // 转换为小数
          mode: 'scale',
          customMode: 'scale',
        });
      } else if (customMode === 'dimensions') {
        // 自定义尺寸
        const widthNum = parseFloat(customWidth);
        const heightNum = parseFloat(customHeight);

        if (
          isNaN(widthNum) ||
          isNaN(heightNum) ||
          widthNum <= 0 ||
          heightNum <= 0
        ) {
          showTips('error', 'Please enter valid width and height values.');
          return;
        }

        // 转换为像素
        const widthPx = convertToPixels(widthNum, unit);
        const heightPx = convertToPixels(heightNum, unit);

        // 验证尺寸
        const error = validateDimensions(widthPx, heightPx);
        if (error) {
          showTips('error', error);
          return;
        }

        onApply({
          width: Math.round(widthPx),
          height: Math.round(heightPx),
          mode: 'custom',
          customMode: 'dimensions',
          unit,
          lockAspectRatio,
        });
      }
      return;
    }

    // 其他分类的处理
    const category = RESIZE_PRESETS[selectedCategory];
    const preset = category.presets.find(p => p.id === selectedPreset);

    if (preset && onApply) {
      if (preset.mode === 'fixed') {
        // 固定尺寸模式
        const fixedPreset = preset as FixedPreset;
        onApply({
          width: fixedPreset.width,
          height: fixedPreset.height,
          mode: 'fixed',
        });
      } else if (preset.mode === 'ratio') {
        // 宽高比模式
        const ratioPreset = preset as RatioPreset;
        onApply({
          aspectRatio: ratioPreset.aspectRatio,
          mode: 'ratio',
        });
      }
    }
  };

  return (
    <div className='w-[344px] bg-white border-r border-[#E7E7E7] flex flex-col h-full'>
      {/* 头部标题区域 */}
      <div className='pt-6 px-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>Resize</h2>
        </div>
      </div>

      {/* 分类选择下拉框 */}
      <div className='px-4 pt-4'>
        <Select
          value={selectedCategory}
          onValueChange={(
            value: 'marketplace' | 'social' | 'ratio' | 'custom'
          ) => {
            setSelectedCategory(value);
            // Custom Size 分类不需要预设，直接显示控制选项
            if (value === 'custom') {
              setSelectedPreset(''); // 清空预设选择
            } else {
              // 其他分类切换时选择该分类下的第一个预设
              const firstPreset = RESIZE_PRESETS[value].presets[0];
              if (firstPreset) {
                setSelectedPreset(firstPreset.id);
              }
            }
          }}
        >
          <SelectTrigger
            size='lg'
            className='w-[312px] h-12 bg-white border border-[#E7E7E7] hover:border-[#E7E7E7] data-[state=open]:border-[#FFCC03] rounded-lg px-3 flex items-center justify-between gap-0.5 text-base font-normal text-text-primary transition-colors'
          >
            <SelectValue placeholder='Select a category' />
          </SelectTrigger>
          <SelectContent className='bg-white border border-[#E7E7E7] rounded-xl shadow-lg py-2 px-2'>
            <SelectItem
              value='marketplace'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              For Marketplace Platforms
            </SelectItem>
            <SelectItem
              value='social'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              For Social Media Platforms
            </SelectItem>
            <SelectItem value='ratio' className='h-10 mb-1 last:mb-0 text-base'>
              Ratio Size
            </SelectItem>
            <SelectItem
              value='custom'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              Custom Size
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 预设列表 */}
      <div className='flex-1 overflow-y-auto px-4 pt-2'>
        {selectedCategory === 'custom' ? (
          // Custom Size 分类直接显示控制选项
          <div className='space-y-4'>
            {/* Radio 选择 */}
            <div className='bg-white rounded-lg p-0'>
              <div className='flex bg-[#F5F5F5] rounded-lg p-1'>
                <button
                  onClick={() => setCustomMode('scale')}
                  className={cn(
                    'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors',
                    customMode === 'scale'
                      ? 'bg-white text-[#121212] shadow-sm'
                      : 'text-[#878787] hover:text-[#121212]'
                  )}
                >
                  By Scale
                </button>
                <button
                  onClick={() => setCustomMode('dimensions')}
                  className={cn(
                    'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors',
                    customMode === 'dimensions'
                      ? 'bg-white text-[#121212] shadow-sm'
                      : 'text-[#878787] hover:text-[#121212]'
                  )}
                >
                  By Dimensions
                </button>
              </div>
            </div>

            {/* By Scale 内容 */}
            {customMode === 'scale' && (
              <div className='space-y-3'>
                <div className='flex items-center gap-3'>
                  <Slider
                    value={[scaleValue]}
                    onValueChange={value => setScaleValue(value[0])}
                    min={20}
                    max={200}
                    step={1}
                    variant='brand'
                    className='flex-1'
                  />
                  <div className='text-sm font-medium text-[#121212] w-12 text-right'>
                    {scaleValue}%
                  </div>
                </div>
              </div>
            )}

            {/* By Dimensions 内容 */}
            {customMode === 'dimensions' && (
              <div className='space-y-3'>
                {/* 宽度和高度输入 */}
                <div className='flex items-center gap-1'>
                  {/* 宽度输入框 */}
                  <div className='flex-1'>
                    <div className='relative'>
                      <Input
                        type='number'
                        value={customWidth}
                        onChange={e => {
                          // 只允许输入数字和小数点
                          const value = e.target.value;
                          if (value === '' || /^\d*\.?\d*$/.test(value)) {
                            setCustomWidth(value);
                            let newHeight = customHeight;

                            // 如果锁定宽高比，自动计算高度
                            if (lockAspectRatio && value && images.length > 0) {
                              const firstImage = images[0];
                              if (firstImage.width && firstImage.height) {
                                const aspectRatio =
                                  firstImage.width / firstImage.height;
                                const calculatedHeight =
                                  parseFloat(value) / aspectRatio;
                                newHeight = calculatedHeight.toFixed(0);
                                setCustomHeight(newHeight);
                              }
                            }

                            // 实时验证 - 如果有错误则显示 tips
                            const widthNum = parseFloat(value);
                            const heightNum = parseFloat(newHeight);
                            if (
                              !isNaN(widthNum) &&
                              !isNaN(heightNum) &&
                              widthNum > 0 &&
                              heightNum > 0
                            ) {
                              const widthPx = convertToPixels(widthNum, unit);
                              const heightPx = convertToPixels(heightNum, unit);
                              const error = validateDimensions(
                                widthPx,
                                heightPx
                              );
                              if (error) {
                                showTips('error', error);
                              }
                            }
                          }
                        }}
                        placeholder='0'
                        className='h-10 pr-16 text-sm border-[#E7E7E7] rounded-lg'
                      />
                      <div className='absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#878787] pointer-events-none'>
                        Width
                      </div>
                    </div>
                  </div>

                  {/* 锁定按钮 */}
                  <button
                    onClick={() => setLockAspectRatio(!lockAspectRatio)}
                    className='w-6 h-6 flex items-center justify-center rounded border border-[#E7E7E7] hover:bg-gray-50 transition-colors'
                    title={
                      lockAspectRatio
                        ? 'Unlock Aspect Ratio'
                        : 'Lock Aspect Ratio'
                    }
                  >
                    {lockAspectRatio ? (
                      <Lock className='w-4 h-4 text-[#121212]' />
                    ) : (
                      <Unlock className='w-4 h-4 text-[#121212]' />
                    )}
                  </button>

                  {/* 高度输入框 */}
                  <div className='flex-1'>
                    <div className='relative'>
                      <Input
                        type='number'
                        value={customHeight}
                        onChange={e => {
                          // 只允许输入数字和小数点
                          const value = e.target.value;
                          if (value === '' || /^\d*\.?\d*$/.test(value)) {
                            setCustomHeight(value);
                            let newWidth = customWidth;

                            // 如果锁定宽高比，自动计算宽度
                            if (lockAspectRatio && value && images.length > 0) {
                              const firstImage = images[0];
                              if (firstImage.width && firstImage.height) {
                                const aspectRatio =
                                  firstImage.width / firstImage.height;
                                const calculatedWidth =
                                  parseFloat(value) * aspectRatio;
                                newWidth = calculatedWidth.toFixed(0);
                                setCustomWidth(newWidth);
                              }
                            }

                            // 实时验证 - 如果有错误则显示 tips
                            const widthNum = parseFloat(newWidth);
                            const heightNum = parseFloat(value);
                            if (
                              !isNaN(widthNum) &&
                              !isNaN(heightNum) &&
                              widthNum > 0 &&
                              heightNum > 0
                            ) {
                              const widthPx = convertToPixels(widthNum, unit);
                              const heightPx = convertToPixels(heightNum, unit);
                              const error = validateDimensions(
                                widthPx,
                                heightPx
                              );
                              if (error) {
                                showTips('error', error);
                              }
                            }
                          }
                        }}
                        placeholder='0'
                        className='h-10 pr-16 text-sm border-[#E7E7E7] rounded-lg'
                      />
                      <div className='absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#878787] pointer-events-none'>
                        Height
                      </div>
                    </div>
                  </div>
                </div>

                {/* 单位选择 */}
                <div>
                  <Select
                    value={unit}
                    onValueChange={(value: 'px' | 'in' | 'mm') => {
                      setUnit(value);
                      // 单位改变时重新验证
                      const widthNum = parseFloat(customWidth);
                      const heightNum = parseFloat(customHeight);
                      if (
                        !isNaN(widthNum) &&
                        !isNaN(heightNum) &&
                        widthNum > 0 &&
                        heightNum > 0
                      ) {
                        const widthPx = convertToPixels(widthNum, value);
                        const heightPx = convertToPixels(heightNum, value);
                        const error = validateDimensions(widthPx, heightPx);
                        if (error) {
                          showTips('error', error);
                        }
                      }
                    }}
                  >
                    <SelectTrigger className='w-full h-10 bg-white border border-[#E7E7E7] rounded-lg px-3'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className='bg-white border border-[#E7E7E7] rounded-xl shadow-lg py-2 px-2'>
                      <SelectItem
                        value='px'
                        className='h-10 mb-1 last:mb-0 text-sm'
                      >
                        Pixel (px)
                      </SelectItem>
                      <SelectItem
                        value='in'
                        className='h-10 mb-1 last:mb-0 text-sm'
                      >
                        Inch (in)
                      </SelectItem>
                      <SelectItem
                        value='mm'
                        className='h-10 mb-1 last:mb-0 text-sm'
                      >
                        Millimeter (mm)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
        ) : (
          // 其他分类显示预设列表
          <div className='space-y-2'>
            {RESIZE_PRESETS[selectedCategory].presets.map(preset => (
              <button
                key={preset.id}
                onClick={() => setSelectedPreset(preset.id)}
                className={cn(
                  'w-[312px] h-10 bg-white rounded-lg px-3 flex items-center gap-0.5 text-left transition-colors',
                  selectedPreset === preset.id
                    ? 'bg-blue-50 border border-blue-200'
                    : 'hover:bg-gray-100'
                )}
              >
                <div className='flex items-center gap-2 w-[288px]'>
                  <div className='w-7 h-7 bg-white border border-[#E7E7E7] rounded flex items-center justify-center overflow-hidden'>
                    {preset.icon.startsWith('/') ? (
                      <Image
                        src={preset.icon}
                        alt={preset.name}
                        width={28}
                        height={28}
                        className='object-contain w-full h-full'
                      />
                    ) : (
                      <div className='text-xs'>{preset.icon}</div>
                    )}
                  </div>
                  <div className='flex items-center justify-between flex-1'>
                    <div className='text-sm font-normal text-[#000000] w-40'>
                      {preset.name}
                    </div>
                    <div className='text-sm font-normal text-[#878787] w-[84px] text-right'>
                      {preset.mode === 'fixed'
                        ? `${(preset as FixedPreset).width}x${(preset as FixedPreset).height}`
                        : preset.mode === 'ratio'
                          ? 'Ratio'
                          : 'Custom'}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 底部应用按钮 */}
      <div className='px-4 py-6 bg-white rounded-b-2xl'>
        <Button
          onClick={handleApply}
          disabled={shouldDisableBatchApplyButton(isProcessing, images)}
          className='w-full h-12 bg-[#FFCC03] hover:bg-[#FFCC03]/90 text-[#121212] text-base font-medium rounded-xl disabled:opacity-50'
          size='lg'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={16}
                height={16}
                className='animate-spin'
              />
              Processing...
            </div>
          ) : (
            'Apply'
          )}
        </Button>
      </div>
    </div>
  );
}
